import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Keyboard,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { colors, typography, spacing } from '../constants/theme';
import { 
  addressAutocompleteService, 
  AddressSuggestion, 
  AddressDetails 
} from '../services/addressAutocompleteService';
import { UserAddress } from '../services/smartFormAutocompletion';

interface SmartAddressInputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  onAddressSelected: (address: AddressDetails) => void;
  placeholder?: string;
  error?: string;
  required?: boolean;
  savedAddresses?: UserAddress[];
  onSaveAddress?: (address: AddressDetails, label: string) => void;
  showSavedAddresses?: boolean;
  country?: string;
  region?: string;
  style?: any;
}

const SmartAddressInput: React.FC<SmartAddressInputProps> = ({
  label,
  value,
  onChangeText,
  onAddressSelected,
  placeholder = 'Commencez à taper votre adresse...',
  error,
  required = false,
  savedAddresses = [],
  onSaveAddress,
  showSavedAddresses = true,
  country = 'ci',
  region,
  style,
}) => {
  const [suggestions, setSuggestions] = useState<AddressSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showSavedModal, setShowSavedModal] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<AddressDetails | null>(null);
  const [saveAddressLabel, setSaveAddressLabel] = useState('');
  const [showSaveModal, setShowSaveModal] = useState(false);
  
  const inputRef = useRef<TextInput>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (value.length >= 3) {
      // Debounce pour éviter trop de requêtes
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }

      debounceRef.current = setTimeout(() => {
        searchAddresses(value);
      }, 300);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [value]);

  const searchAddresses = async (query: string) => {
    try {
      setLoading(true);
      const results = await addressAutocompleteService.getAddressSuggestions(query, {
        country,
        region,
        types: ['address', 'establishment'],
      });

      setSuggestions(results);
      setShowSuggestions(results.length > 0);
    } catch (error) {
      console.error('Erreur recherche adresses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSuggestionPress = async (suggestion: AddressSuggestion) => {
    try {
      setLoading(true);
      const details = await addressAutocompleteService.getAddressDetails(suggestion.placeId);
      
      if (details) {
        onChangeText(details.formattedAddress);
        onAddressSelected(details);
        setSelectedAddress(details);
        setShowSuggestions(false);
        Keyboard.dismiss();
      }
    } catch (error) {
      console.error('Erreur récupération détails adresse:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSavedAddressPress = (savedAddress: UserAddress) => {
    onChangeText(savedAddress.addressDetails.formattedAddress);
    onAddressSelected(savedAddress.addressDetails);
    setShowSavedModal(false);
  };

  const handleSaveAddress = () => {
    if (selectedAddress && saveAddressLabel.trim() && onSaveAddress) {
      onSaveAddress(selectedAddress, saveAddressLabel.trim());
      setShowSaveModal(false);
      setSaveAddressLabel('');
    }
  };

  const renderSuggestion = ({ item }: { item: AddressSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}
    >
      <View style={styles.suggestionIcon}>
        <Ionicons name="location-outline" size={20} color={colors.primary[500]} />
      </View>
      <View style={styles.suggestionContent}>
        <Text style={styles.suggestionMainText}>{item.mainText}</Text>
        <Text style={styles.suggestionSecondaryText}>{item.secondaryText}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderSavedAddress = ({ item }: { item: UserAddress }) => (
    <TouchableOpacity
      style={styles.savedAddressItem}
      onPress={() => handleSavedAddressPress(item)}
    >
      <View style={styles.savedAddressIcon}>
        <Ionicons 
          name={item.isDefault ? 'home' : 'location'} 
          size={20} 
          color={item.isDefault ? colors.primary[500] : colors.text.secondary} 
        />
      </View>
      <View style={styles.savedAddressContent}>
        <View style={styles.savedAddressHeader}>
          <Text style={styles.savedAddressLabel}>{item.label}</Text>
          {item.isDefault && (
            <View style={styles.defaultBadge}>
              <Text style={styles.defaultBadgeText}>Par défaut</Text>
            </View>
          )}
        </View>
        <Text style={styles.savedAddressText} numberOfLines={2}>
          {addressAutocompleteService.formatAddressForDisplay(item.addressDetails)}
        </Text>
      </View>
      <View style={styles.savedAddressUsage}>
        <Text style={styles.usageCount}>{item.usageCount}</Text>
        <Text style={styles.usageLabel}>fois</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, style]}>
      {/* Label */}
      <View style={styles.labelContainer}>
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
        {showSavedAddresses && savedAddresses.length > 0 && (
          <TouchableOpacity
            style={styles.savedAddressesButton}
            onPress={() => setShowSavedModal(true)}
          >
            <Ionicons name="bookmark-outline" size={16} color={colors.primary[500]} />
            <Text style={styles.savedAddressesButtonText}>Adresses sauvées</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Input Container */}
      <View style={[styles.inputContainer, error && styles.inputContainerError]}>
        <View style={styles.inputIcon}>
          <Ionicons name="location-outline" size={20} color={colors.text.secondary} />
        </View>
        
        <TextInput
          ref={inputRef}
          style={styles.textInput}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={colors.text.disabled}
          onFocus={() => {
            if (suggestions.length > 0) {
              setShowSuggestions(true);
            }
          }}
          multiline
          numberOfLines={2}
        />

        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={colors.primary[500]} />
          </View>
        )}

        {selectedAddress && onSaveAddress && (
          <TouchableOpacity
            style={styles.saveButton}
            onPress={() => setShowSaveModal(true)}
          >
            <Ionicons name="bookmark-outline" size={20} color={colors.primary[500]} />
          </TouchableOpacity>
        )}
      </View>

      {/* Error Message */}
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      {/* Suggestions List */}
      {showSuggestions && suggestions.length > 0 && (
        <View style={styles.suggestionsContainer}>
          <FlatList
            data={suggestions}
            renderItem={renderSuggestion}
            keyExtractor={(item) => item.id}
            style={styles.suggestionsList}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          />
        </View>
      )}

      {/* Saved Addresses Modal */}
      <Modal
        visible={showSavedModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowSavedModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Adresses sauvegardées</Text>
              <TouchableOpacity
                onPress={() => setShowSavedModal(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color={colors.text.secondary} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={savedAddresses}
              renderItem={renderSavedAddress}
              keyExtractor={(item) => item.id}
              style={styles.savedAddressesList}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </Modal>

      {/* Save Address Modal */}
      <Modal
        visible={showSaveModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSaveModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.saveModalContainer}>
            <Text style={styles.saveModalTitle}>Sauvegarder cette adresse</Text>
            
            <TextInput
              style={styles.saveModalInput}
              value={saveAddressLabel}
              onChangeText={setSaveAddressLabel}
              placeholder="Nom de l'adresse (ex: Domicile, Bureau...)"
              placeholderTextColor={colors.text.disabled}
            />

            <View style={styles.saveModalButtons}>
              <TouchableOpacity
                style={styles.saveModalCancelButton}
                onPress={() => setShowSaveModal(false)}
              >
                <Text style={styles.saveModalCancelText}>Annuler</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.saveModalSaveButton,
                  !saveAddressLabel.trim() && styles.saveModalSaveButtonDisabled,
                ]}
                onPress={handleSaveAddress}
                disabled={!saveAddressLabel.trim()}
              >
                <Text style={styles.saveModalSaveText}>Sauvegarder</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  label: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
  },
  required: {
    color: colors.error,
  },
  savedAddressesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  savedAddressesButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary[500],
    fontWeight: typography.fontWeight.medium,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.neutral[300],
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 56,
  },
  inputContainerError: {
    borderColor: colors.error,
  },
  inputIcon: {
    marginRight: spacing.sm,
    marginTop: 2,
  },
  textInput: {
    flex: 1,
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    textAlignVertical: 'top',
    minHeight: 40,
  },
  loadingContainer: {
    marginLeft: spacing.sm,
    marginTop: 2,
  },
  saveButton: {
    marginLeft: spacing.sm,
    marginTop: 2,
  },
  errorText: {
    fontSize: typography.fontSize.sm,
    color: colors.error,
    marginTop: spacing.xs,
  },
  suggestionsContainer: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    marginTop: spacing.xs,
    maxHeight: 200,
    borderWidth: 1,
    borderColor: colors.neutral[200],
    elevation: 3,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  suggestionsList: {
    flex: 1,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  suggestionIcon: {
    marginRight: spacing.md,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionMainText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: 2,
  },
  suggestionSecondaryText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  modalCloseButton: {
    padding: spacing.xs,
  },
  savedAddressesList: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  savedAddressItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  savedAddressIcon: {
    marginRight: spacing.md,
  },
  savedAddressContent: {
    flex: 1,
  },
  savedAddressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  savedAddressLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginRight: spacing.sm,
  },
  defaultBadge: {
    backgroundColor: colors.primary[100],
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 8,
  },
  defaultBadgeText: {
    fontSize: typography.fontSize.xs,
    color: colors.primary[700],
    fontWeight: typography.fontWeight.medium,
  },
  savedAddressText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 18,
  },
  savedAddressUsage: {
    alignItems: 'center',
    marginLeft: spacing.sm,
  },
  usageCount: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[500],
  },
  usageLabel: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  saveModalContainer: {
    backgroundColor: colors.background.primary,
    marginHorizontal: spacing.lg,
    borderRadius: 16,
    padding: spacing.lg,
    alignSelf: 'center',
    width: '90%',
    maxWidth: 400,
  },
  saveModalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  saveModalInput: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.neutral[300],
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    marginBottom: spacing.lg,
  },
  saveModalButtons: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  saveModalCancelButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.neutral[300],
    alignItems: 'center',
  },
  saveModalCancelText: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    fontWeight: typography.fontWeight.medium,
  },
  saveModalSaveButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: 12,
    backgroundColor: colors.primary[500],
    alignItems: 'center',
  },
  saveModalSaveButtonDisabled: {
    backgroundColor: colors.neutral[300],
  },
  saveModalSaveText: {
    fontSize: typography.fontSize.md,
    color: colors.text.inverse,
    fontWeight: typography.fontWeight.semibold,
  },
});

export default SmartAddressInput;
