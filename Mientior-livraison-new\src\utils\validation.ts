// Utilitaires de validation pour les formulaires

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

// Validation d'email
export const validateEmail = (email: string): ValidationResult => {
  if (!email || !email.trim()) {
    return { isValid: false, error: 'L\'adresse email est requise' };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email.trim())) {
    return { isValid: false, error: 'Format d\'email invalide' };
  }

  return { isValid: true };
};

// Validation de téléphone
export const validatePhone = (phone: string): ValidationResult => {
  if (!phone || !phone.trim()) {
    return { isValid: false, error: 'Le numéro de téléphone est requis' };
  }

  // Supprimer les espaces et caractères spéciaux
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
  
  // Vérifier le format international ou local
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  if (!phoneRegex.test(cleanPhone)) {
    return { isValid: false, error: 'Format de téléphone invalide' };
  }

  // Vérifier la longueur minimale
  if (cleanPhone.length < 8) {
    return { isValid: false, error: 'Le numéro de téléphone est trop court' };
  }

  return { isValid: true };
};

// Validation de nom complet
export const validateFullName = (name: string): ValidationResult => {
  if (!name || !name.trim()) {
    return { isValid: false, error: 'Le nom complet est requis' };
  }

  if (name.trim().length < 2) {
    return { isValid: false, error: 'Le nom doit contenir au moins 2 caractères' };
  }

  if (name.trim().length > 100) {
    return { isValid: false, error: 'Le nom est trop long (max 100 caractères)' };
  }

  // Vérifier que le nom contient au moins un prénom et un nom
  const nameParts = name.trim().split(/\s+/);
  if (nameParts.length < 2) {
    return { isValid: false, error: 'Veuillez entrer votre prénom et nom' };
  }

  // Vérifier les caractères autorisés
  const nameRegex = /^[a-zA-ZÀ-ÿ\s'-]+$/;
  if (!nameRegex.test(name.trim())) {
    return { isValid: false, error: 'Le nom contient des caractères non autorisés' };
  }

  return { isValid: true };
};

// Validation de mot de passe
export const validatePassword = (password: string): ValidationResult => {
  if (!password) {
    return { isValid: false, error: 'Le mot de passe est requis' };
  }

  if (password.length < 8) {
    return { isValid: false, error: 'Le mot de passe doit contenir au moins 8 caractères' };
  }

  if (password.length > 128) {
    return { isValid: false, error: 'Le mot de passe est trop long (max 128 caractères)' };
  }

  // Vérifier la complexité
  const hasLowerCase = /[a-z]/.test(password);
  const hasUpperCase = /[A-Z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const complexityScore = [hasLowerCase, hasUpperCase, hasNumbers, hasSpecialChar].filter(Boolean).length;

  if (complexityScore < 3) {
    return { 
      isValid: false, 
      error: 'Le mot de passe doit contenir au moins 3 des éléments suivants: minuscules, majuscules, chiffres, caractères spéciaux' 
    };
  }

  return { isValid: true };
};

// Validation de confirmation de mot de passe
export const validatePasswordConfirmation = (password: string, confirmation: string): ValidationResult => {
  if (!confirmation) {
    return { isValid: false, error: 'La confirmation du mot de passe est requise' };
  }

  if (password !== confirmation) {
    return { isValid: false, error: 'Les mots de passe ne correspondent pas' };
  }

  return { isValid: true };
};

// Calculer la force du mot de passe
export const getPasswordStrength = (password: string): { score: number; label: string; color: string } => {
  if (!password) {
    return { score: 0, label: '', color: '#E5E5E5' };
  }

  let score = 0;
  
  // Longueur
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  
  // Complexité
  if (/[a-z]/.test(password)) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/\d/.test(password)) score += 1;
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;

  // Éviter les patterns communs
  if (!/(.)\1{2,}/.test(password)) score += 1; // Pas de répétitions
  if (!/123|abc|qwe/i.test(password)) score += 1; // Pas de séquences

  const maxScore = 8;
  const percentage = (score / maxScore) * 100;

  if (percentage < 25) {
    return { score: percentage, label: 'Très faible', color: '#FF4444' };
  } else if (percentage < 50) {
    return { score: percentage, label: 'Faible', color: '#FF8800' };
  } else if (percentage < 75) {
    return { score: percentage, label: 'Moyen', color: '#FFBB00' };
  } else {
    return { score: percentage, label: 'Fort', color: '#00AA44' };
  }
};

// Validation d'adresse
export const validateAddress = (address: string): ValidationResult => {
  if (!address || !address.trim()) {
    return { isValid: false, error: 'L\'adresse est requise' };
  }

  if (address.trim().length < 5) {
    return { isValid: false, error: 'L\'adresse est trop courte' };
  }

  if (address.trim().length > 200) {
    return { isValid: false, error: 'L\'adresse est trop longue (max 200 caractères)' };
  }

  return { isValid: true };
};

// Validation de code postal
export const validatePostalCode = (code: string): ValidationResult => {
  if (!code || !code.trim()) {
    return { isValid: false, error: 'Le code postal est requis' };
  }

  // Format pour la Côte d'Ivoire (peut être adapté selon le pays)
  const postalCodeRegex = /^\d{5}$/;
  if (!postalCodeRegex.test(code.trim())) {
    return { isValid: false, error: 'Format de code postal invalide (5 chiffres)' };
  }

  return { isValid: true };
};

// Validation d'âge
export const validateAge = (birthDate: string): ValidationResult => {
  if (!birthDate) {
    return { isValid: false, error: 'La date de naissance est requise' };
  }

  const birth = new Date(birthDate);
  const today = new Date();
  
  if (birth > today) {
    return { isValid: false, error: 'La date de naissance ne peut pas être dans le futur' };
  }

  const age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    // age--;
  }

  if (age < 13) {
    return { isValid: false, error: 'Vous devez avoir au moins 13 ans' };
  }

  if (age > 120) {
    return { isValid: false, error: 'Âge invalide' };
  }

  return { isValid: true };
};

// Validation de formulaire de profil
export const validateProfileForm = (data: {
  full_name?: string;
  email?: string;
  phone?: string;
  address?: string;
}): FormValidationResult => {
  const errors: Record<string, string> = {};

  if (data.full_name !== undefined) {
    const nameValidation = validateFullName(data.full_name);
    if (!nameValidation.isValid) {
      errors.full_name = nameValidation.error!;
    }
  }

  if (data.email !== undefined) {
    const emailValidation = validateEmail(data.email);
    if (!emailValidation.isValid) {
      errors.email = emailValidation.error!;
    }
  }

  if (data.phone !== undefined) {
    const phoneValidation = validatePhone(data.phone);
    if (!phoneValidation.isValid) {
      errors.phone = phoneValidation.error!;
    }
  }

  if (data.address !== undefined) {
    const addressValidation = validateAddress(data.address);
    if (!addressValidation.isValid) {
      errors.address = addressValidation.error!;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

// Nettoyer et formater un numéro de téléphone
export const formatPhoneNumber = (phone: string): string => {
  // Supprimer tous les caractères non numériques sauf le +
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  // Si le numéro commence par +225 (Côte d'Ivoire), formater
  if (cleaned.startsWith('+225')) {
    const number = cleaned.substring(4);
    if (number.length === 8) {
      return `+225 ${number.substring(0, 2)} ${number.substring(2, 4)} ${number.substring(4, 6)} ${number.substring(6, 8)}`;
    }
  }
  
  return cleaned;
};

// Nettoyer et formater un email
export const formatEmail = (email: string): string => {
  return email.trim().toLowerCase();
};

// Validation spécifique pour les adresses
export const validateAddressLabel = (label: string): ValidationResult => {
  if (!label || !label.trim()) {
    return { isValid: false, error: 'Le libellé de l\'adresse est requis' };
  }

  if (label.trim().length < 2) {
    return { isValid: false, error: 'Le libellé doit contenir au moins 2 caractères' };
  }

  if (label.trim().length > 50) {
    return { isValid: false, error: 'Le libellé est trop long (max 50 caractères)' };
  }

  return { isValid: true };
};

// Validation des coordonnées GPS
export const validateCoordinates = (coordinates: { latitude: number; longitude: number }): ValidationResult => {
  if (!coordinates || typeof coordinates.latitude !== 'number' || typeof coordinates.longitude !== 'number') {
    return { isValid: false, error: 'Coordonnées GPS invalides' };
  }

  if (coordinates.latitude < -90 || coordinates.latitude > 90) {
    return { isValid: false, error: 'Latitude invalide (doit être entre -90 et 90)' };
  }

  if (coordinates.longitude < -180 || coordinates.longitude > 180) {
    return { isValid: false, error: 'Longitude invalide (doit être entre -180 et 180)' };
  }

  return { isValid: true };
};

// Validation du type d'adresse
export const validateAddressType = (type: string): ValidationResult => {
  const validTypes = ['home', 'work', 'other'];

  if (!type || !validTypes.includes(type)) {
    return { isValid: false, error: 'Type d\'adresse invalide' };
  }

  return { isValid: true };
};

// Validation complète d'une adresse
export const validateCompleteAddress = (addressData: {
  label: string;
  address: string;
  coordinates: { latitude: number; longitude: number };
  address_type: string;
  details?: string;
}): FormValidationResult => {
  const errors: Record<string, string> = {};

  // Validation du libellé
  const labelValidation = validateAddressLabel(addressData.label);
  if (!labelValidation.isValid) {
    errors.label = labelValidation.error!;
  }

  // Validation de l'adresse
  const addressValidation = validateAddress(addressData.address);
  if (!addressValidation.isValid) {
    errors.address = addressValidation.error!;
  }

  // Validation des coordonnées
  const coordinatesValidation = validateCoordinates(addressData.coordinates);
  if (!coordinatesValidation.isValid) {
    errors.coordinates = coordinatesValidation.error!;
  }

  // Validation du type
  const typeValidation = validateAddressType(addressData.address_type);
  if (!typeValidation.isValid) {
    errors.address_type = typeValidation.error!;
  }

  // Validation des détails (optionnel)
  if (addressData.details && addressData.details.length > 200) {
    errors.details = 'Les détails sont trop longs (max 200 caractères)';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

// Formater une adresse pour l'affichage
export const formatAddressDisplay = (address: {
  street?: string;
  city?: string;
  postal_code?: string;
  country?: string;
  formatted_address?: string;
}): string => {
  if (address.formatted_address) {
    return address.formatted_address;
  }

  const parts = [
    address.street,
    address.city,
    address.postal_code,
    address.country,
  ].filter(Boolean);

  return parts.join(', ');
};

// Normaliser une adresse
export const normalizeAddress = (address: string): string => {
  return address
    .trim()
    .replace(/\s+/g, ' ') // Remplacer les espaces multiples par un seul
    .replace(/,\s*,/g, ',') // Supprimer les virgules doubles
    .replace(/^,|,$/g, ''); // Supprimer les virgules en début/fin
};

// Export pour CommonJS (pour les tests Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    validateEmail,
    validatePhone,
    validateFullName,
    validatePassword,
    validatePasswordConfirmation,
    getPasswordStrength,
    validateAddress,
    validatePostalCode,
    validateAge,
    validateProfileForm,
    formatPhoneNumber,
    formatEmail,
  };
}
