-- Migration: Specialized Services Database Schema
-- Description: Create tables for dynamic specialized delivery services
-- Date: 2024-12-19

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- =====================================================
-- SPECIALIZED SERVICE CATEGORIES TABLE
-- =====================================================
CREATE TABLE specialized_service_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    color VARCHAR(7), -- Hex color code
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    special_requirements JSONB DEFAULT '[]'::jsonb,
    partner_requirements JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SPECIALIZED SERVICES TABLE
-- =====================================================
CREATE TABLE specialized_services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES specialized_service_categories(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    
    -- Pricing configuration
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'XOF',
    pricing_model VARCHAR(50) NOT NULL DEFAULT 'fixed', -- fixed, weight, size, distance, value_percentage, time
    
    -- Pricing tiers (JSONB for flexibility)
    weight_tiers JSONB DEFAULT '[]'::jsonb,
    size_tiers JSONB DEFAULT '[]'::jsonb,
    distance_tiers JSONB DEFAULT '[]'::jsonb,
    subscription_plans JSONB DEFAULT '[]'::jsonb,
    additional_fees JSONB DEFAULT '{}'::jsonb,
    discounts JSONB DEFAULT '{}'::jsonb,
    
    -- Availability configuration
    available_cities JSONB DEFAULT '[]'::jsonb,
    time_slots JSONB DEFAULT '[]'::jsonb,
    max_delivery_time INTEGER, -- in minutes
    min_delivery_time INTEGER, -- in minutes
    emergency_available BOOLEAN DEFAULT false,
    subscription_available BOOLEAN DEFAULT false,
    
    -- Special handling options
    fragile_items BOOLEAN DEFAULT false,
    temperature_control BOOLEAN DEFAULT false,
    secure_transport BOOLEAN DEFAULT false,
    special_packaging BOOLEAN DEFAULT false,
    signature_required BOOLEAN DEFAULT false,
    photo_proof BOOLEAN DEFAULT false,
    real_time_tracking BOOLEAN DEFAULT false,
    insurance_coverage BOOLEAN DEFAULT false,
    custom_instructions BOOLEAN DEFAULT false,
    
    -- Partner integration
    partner_type VARCHAR(100),
    verification_required BOOLEAN DEFAULT false,
    inventory_integration BOOLEAN DEFAULT false,
    appointment_booking BOOLEAN DEFAULT false,
    custom_workflow BOOLEAN DEFAULT false,
    quality_standards JSONB DEFAULT '[]'::jsonb,
    certification_required JSONB DEFAULT '[]'::jsonb,
    
    -- Features and requirements
    features JSONB DEFAULT '[]'::jsonb,
    requirements JSONB DEFAULT '[]'::jsonb,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- USER PREFERENCES TABLE
-- =====================================================
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    
    -- Delivery preferences
    preferred_delivery_time VARCHAR(20), -- morning, afternoon, evening, anytime
    preferred_services JSONB DEFAULT '[]'::jsonb, -- Array of service IDs
    frequent_options JSONB DEFAULT '[]'::jsonb, -- Frequently selected options
    
    -- Contact preferences
    primary_phone VARCHAR(20),
    alternative_phone VARCHAR(20),
    preferred_contact_method VARCHAR(20), -- sms, call, whatsapp
    
    -- Payment preferences
    preferred_payment_method VARCHAR(20), -- cash, mobile_money, card
    mobile_money_number VARCHAR(20),
    
    -- Delivery instructions
    delivery_instructions JSONB DEFAULT '[]'::jsonb,
    
    -- Subscriptions
    active_subscriptions JSONB DEFAULT '[]'::jsonb,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- =====================================================
-- USER ADDRESSES TABLE
-- =====================================================
CREATE TABLE user_addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    label VARCHAR(100) NOT NULL, -- Home, Office, Other
    
    -- Address details (from Google Places)
    place_id VARCHAR(255),
    formatted_address TEXT NOT NULL,
    street_number VARCHAR(50),
    street_name VARCHAR(255),
    locality VARCHAR(255), -- City
    sublocality VARCHAR(255), -- Neighborhood
    administrative_area_level_1 VARCHAR(255), -- Region/State
    administrative_area_level_2 VARCHAR(255), -- Department
    country VARCHAR(255),
    postal_code VARCHAR(20),
    
    -- Coordinates
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 1,
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SERVICE ORDERS TABLE
-- =====================================================
CREATE TABLE specialized_service_orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    service_id UUID NOT NULL REFERENCES specialized_services(id),
    category_id UUID NOT NULL REFERENCES specialized_service_categories(id),
    
    -- Order details
    order_details JSONB NOT NULL, -- Items, instructions, addresses, etc.
    selected_options JSONB DEFAULT '[]'::jsonb,
    urgent_delivery BOOLEAN DEFAULT false,
    pickup_required BOOLEAN DEFAULT false,
    
    -- Pricing breakdown
    pricing JSONB NOT NULL, -- Complete pricing breakdown
    
    -- Addresses
    pickup_address_id UUID REFERENCES user_addresses(id),
    delivery_address_id UUID REFERENCES user_addresses(id),
    pickup_address_text TEXT,
    delivery_address_text TEXT NOT NULL,
    
    -- Scheduling
    scheduled_time TIMESTAMP WITH TIME ZONE,
    estimated_delivery_time TIMESTAMP WITH TIME ZONE,
    actual_delivery_time TIMESTAMP WITH TIME ZONE,
    
    -- Status tracking
    status VARCHAR(50) DEFAULT 'pending', -- pending, confirmed, in_progress, delivered, cancelled
    status_history JSONB DEFAULT '[]'::jsonb,
    
    -- Contact information
    contact_phone VARCHAR(20) NOT NULL,
    alternative_contact VARCHAR(20),
    
    -- Special instructions
    special_instructions TEXT,
    
    -- Tracking information
    tracking_info JSONB DEFAULT '{}'::jsonb,
    
    -- Payment information
    payment_method VARCHAR(20) DEFAULT 'cash',
    payment_status VARCHAR(20) DEFAULT 'pending',
    payment_details JSONB DEFAULT '{}'::jsonb,
    
    -- Partner assignment
    assigned_partner_id UUID,
    partner_notes TEXT,
    
    -- Quality and feedback
    customer_rating INTEGER CHECK (customer_rating >= 1 AND customer_rating <= 5),
    customer_feedback TEXT,
    partner_rating INTEGER CHECK (partner_rating >= 1 AND partner_rating <= 5),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SERVICE PARTNERS TABLE
-- =====================================================
CREATE TABLE service_partners (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    partner_type VARCHAR(100) NOT NULL, -- pharmacy, bakery, dry_cleaner, etc.
    
    -- Contact information
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    
    -- Location
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    service_radius INTEGER DEFAULT 5000, -- in meters
    
    -- Capabilities
    supported_services JSONB DEFAULT '[]'::jsonb, -- Array of service IDs
    certifications JSONB DEFAULT '[]'::jsonb,
    quality_score DECIMAL(3,2) DEFAULT 0,
    
    -- Availability
    operating_hours JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    accepts_new_orders BOOLEAN DEFAULT true,
    
    -- Performance metrics (for admin only)
    total_orders INTEGER DEFAULT 0,
    completed_orders INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Categories
CREATE INDEX idx_categories_active ON specialized_service_categories(is_active, display_order);

-- Services
CREATE INDEX idx_services_category ON specialized_services(category_id, is_active, display_order);
CREATE INDEX idx_services_pricing_model ON specialized_services(pricing_model);
CREATE INDEX idx_services_partner_type ON specialized_services(partner_type);

-- User preferences
CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);

-- User addresses
CREATE INDEX idx_user_addresses_user_id ON user_addresses(user_id, is_active);
CREATE INDEX idx_user_addresses_default ON user_addresses(user_id, is_default) WHERE is_default = true;
CREATE INDEX idx_user_addresses_location ON user_addresses USING GIST(ST_Point(longitude, latitude));

-- Service orders
CREATE INDEX idx_service_orders_user_id ON specialized_service_orders(user_id, created_at DESC);
CREATE INDEX idx_service_orders_service_id ON specialized_service_orders(service_id, created_at DESC);
CREATE INDEX idx_service_orders_status ON specialized_service_orders(status, created_at DESC);
CREATE INDEX idx_service_orders_partner ON specialized_service_orders(assigned_partner_id, status);

-- Service partners
CREATE INDEX idx_service_partners_type ON service_partners(partner_type, is_active);
CREATE INDEX idx_service_partners_location ON service_partners USING GIST(ST_Point(longitude, latitude));
CREATE INDEX idx_service_partners_active ON service_partners(is_active, accepts_new_orders);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to all tables
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON specialized_service_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON specialized_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_addresses_updated_at BEFORE UPDATE ON user_addresses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_service_orders_updated_at BEFORE UPDATE ON specialized_service_orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_service_partners_updated_at BEFORE UPDATE ON service_partners FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
