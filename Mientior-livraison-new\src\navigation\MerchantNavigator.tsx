import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

// Types
import { MerchantTabParamList } from '../types';

// Écrans placeholders pour les marchands
import MerchantHomeScreen from '../screens/merchant/MerchantHomeScreen';
import MerchantOrdersScreen from '../screens/merchant/MerchantOrdersScreen';
import MerchantProductsScreen from '../screens/merchant/MerchantProductsScreen';
import MerchantProfileScreen from '../screens/merchant/MerchantProfileScreen';
import EditProfileScreen from '../screens/shared/EditProfileScreen';
import PreferencesScreen from '../screens/shared/PreferencesScreen';

const Tab = createBottomTabNavigator<MerchantTabParamList>();
const Stack = createStackNavigator();

// Navigation par onglets pour les marchands
const MerchantTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'MerchantHome':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'MerchantOrders':
              iconName = focused ? 'list' : 'list-outline';
              break;
            case 'MerchantProducts':
              iconName = focused ? 'grid' : 'grid-outline';
              break;
            case 'MerchantProfile':
              iconName = focused ? 'person' : 'person-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: '#8E8E93',
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="MerchantHome"
        component={MerchantHomeScreen}
        options={{ title: 'Accueil' }}
      />
      <Tab.Screen
        name="MerchantOrders"
        component={MerchantOrdersScreen}
        options={{ title: 'Commandes' }}
      />
      <Tab.Screen
        name="MerchantProducts"
        component={MerchantProductsScreen}
        options={{ title: 'Produits' }}
      />
      <Tab.Screen
        name="MerchantProfile"
        component={MerchantProfileScreen}
        options={{ title: 'Profil' }}
      />
    </Tab.Navigator>
  );
};

// Navigation principale avec pile pour les détails
const MerchantNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      {/* Navigation par onglets comme écran principal */}
      <Stack.Screen
        name="MerchantTabs"
        component={MerchantTabNavigator}
        options={{ headerShown: false }}
      />

      {/* Écran de modification du profil */}
      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{
          title: 'Modifier le profil',
          headerShown: false,
        }}
      />

      {/* Écran des préférences */}
      <Stack.Screen
        name="Preferences"
        component={PreferencesScreen}
        options={{
          title: 'Préférences',
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default MerchantNavigator; 