-- Migration: Initial Services Data
-- Description: Insert initial specialized service categories and services
-- Date: 2024-12-19

-- =====================================================
-- INSERT SPECIALIZED SERVICE CATEGORIES
-- =====================================================

INSERT INTO specialized_service_categories (id, name, description, icon, color, display_order, special_requirements, partner_requirements) VALUES
(
    '550e8400-e29b-41d4-a716-446655440001',
    'Colis & Marchandises',
    'Livraison sécurisée de colis et marchandises avec traçabilité complète',
    'package-variant',
    '#2563eb',
    1,
    '["tracking", "insurance", "secure_handling"]'::jsonb,
    '["verification", "insurance_coverage", "tracking_system"]'::jsonb
),
(
    '550e8400-e29b-41d4-a716-446655440002',
    'Pharmacie & Santé',
    'Livraison de médicaments et produits de santé avec service pharmaceutique',
    'medical-bag',
    '#dc2626',
    2,
    '["pharmaceutical_license", "temperature_control", "prescription_verification"]'::jsonb,
    '["pharmacy_license", "qualified_pharmacist", "cold_chain"]'::jsonb
),
(
    '550e8400-e29b-41d4-a716-446655440003',
    'Boulangerie & Produits Frais',
    'Livraison de pain frais et produits de boulangerie avec garantie fraîcheur',
    'bread-slice',
    '#f59e0b',
    3,
    '["freshness_guarantee", "temperature_control", "time_sensitive"]'::jsonb,
    '["bakery_certification", "fresh_products", "daily_delivery"]'::jsonb
),
(
    '550e8400-e29b-41d4-a716-446655440004',
    'Pressing & Nettoyage',
    'Service de collecte et livraison pour nettoyage à sec et pressing',
    'tshirt-crew',
    '#7c3aed',
    4,
    '["pickup_required", "quality_guarantee", "care_instructions"]'::jsonb,
    '["dry_cleaning_equipment", "quality_standards", "pickup_service"]'::jsonb
),
(
    '550e8400-e29b-41d4-a716-446655440005',
    'Électronique & Technologie',
    'Livraison sécurisée d\'appareils électroniques et accessoires tech',
    'cellphone',
    '#059669',
    5,
    '["secure_transport", "insurance_required", "signature_required"]'::jsonb,
    '["electronics_handling", "insurance_coverage", "secure_storage"]'::jsonb
),
(
    '550e8400-e29b-41d4-a716-446655440006',
    'Librairie & Papeterie',
    'Livraison de livres, fournitures scolaires et articles de papeterie',
    'book-open-variant',
    '#0891b2',
    6,
    '["careful_handling", "weather_protection"]'::jsonb,
    '["book_handling", "educational_materials"]'::jsonb
),
(
    '550e8400-e29b-41d4-a716-446655440007',
    'Beauté & Coiffure',
    'Services de beauté et coiffure à domicile avec professionnels qualifiés',
    'face-woman',
    '#db2777',
    7,
    '["professional_service", "appointment_booking", "equipment_included"]'::jsonb,
    '["beauty_certification", "professional_equipment", "hygiene_standards"]'::jsonb
),
(
    '550e8400-e29b-41d4-a716-446655440008',
    'Restaurant & Traiteur',
    'Livraison de repas chauds et services de traiteur avec garantie température',
    'silverware-fork-knife',
    '#ea580c',
    8,
    '["temperature_guarantee", "food_safety", "time_sensitive"]'::jsonb,
    '["food_license", "temperature_control", "hygiene_certification"]'::jsonb
);

-- =====================================================
-- INSERT SPECIALIZED SERVICES
-- =====================================================

-- Colis & Marchandises Services
INSERT INTO specialized_services (
    id, category_id, name, description, icon, display_order, base_price, pricing_model,
    weight_tiers, distance_tiers, additional_fees, features, requirements,
    fragile_items, real_time_tracking, insurance_coverage, signature_required,
    partner_type, verification_required
) VALUES
(
    '660e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440001',
    'Livraison Colis Standard',
    'Livraison sécurisée de colis avec traçabilité complète et assurance incluse',
    'package',
    1,
    1500.00,
    'weight',
    '[
        {"minWeight": 0, "maxWeight": 2, "basePrice": 1500, "pricePerKg": 0},
        {"minWeight": 2, "maxWeight": 5, "basePrice": 2000, "pricePerKg": 300},
        {"minWeight": 5, "maxWeight": 10, "basePrice": 3000, "pricePerKg": 400},
        {"minWeight": 10, "maxWeight": 20, "basePrice": 5000, "pricePerKg": 500}
    ]'::jsonb,
    '[
        {"minDistance": 0, "maxDistance": 5, "basePrice": 0, "pricePerKm": 0},
        {"minDistance": 5, "maxDistance": 15, "basePrice": 500, "pricePerKm": 100},
        {"minDistance": 15, "maxDistance": 30, "basePrice": 1500, "pricePerKm": 150}
    ]'::jsonb,
    '{"express_delivery": 1000, "fragile_handling": 500, "signature_required": 300}'::jsonb,
    '["Traçabilité GPS", "Assurance incluse", "Notification SMS", "Photo de livraison"]'::jsonb,
    '["Emballage approprié", "Étiquetage clair"]'::jsonb,
    true,
    true,
    true,
    true,
    'courier',
    true
),
(
    '660e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440001',
    'Objets Fragiles Premium',
    'Transport spécialisé pour objets fragiles avec emballage renforcé',
    'package-variant-closed',
    2,
    3000.00,
    'value_percentage',
    '[]'::jsonb,
    '[]'::jsonb,
    '{"special_packaging": 1000, "insurance_premium": 0, "express_delivery": 1500}'::jsonb,
    '["Emballage professionnel", "Assurance tous risques", "Manipulation experte", "Garantie casse"]'::jsonb,
    '["Déclaration de valeur", "Photos avant transport"]'::jsonb,
    true,
    true,
    true,
    true,
    'specialist_courier',
    true
);

-- Pharmacie & Santé Services
INSERT INTO specialized_services (
    id, category_id, name, description, icon, display_order, base_price, pricing_model,
    additional_fees, features, requirements, temperature_control, real_time_tracking,
    partner_type, verification_required, certification_required
) VALUES
(
    '660e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440002',
    'Livraison sur Ordonnance',
    'Livraison de médicaments sur ordonnance avec vérification pharmaceutique',
    'pill',
    1,
    1200.00,
    'fixed',
    '{"urgent_delivery": 2000, "consultation_fee": 500}'::jsonb,
    '["Vérification ordonnance", "Conseil pharmaceutique", "Confidentialité", "Traçabilité"]'::jsonb,
    '["Ordonnance valide", "Pièce d\'identité"]'::jsonb,
    true,
    true,
    'pharmacy',
    true,
    '["pharmaceutical_license", "qualified_pharmacist"]'::jsonb
),
(
    '660e8400-e29b-41d4-a716-446655440004',
    '550e8400-e29b-41d4-a716-446655440002',
    'Médicaments Sans Ordonnance',
    'Livraison de médicaments en vente libre et produits de parapharmacie',
    'medical-bag',
    2,
    800.00,
    'fixed',
    '{"subscription_discount": -200, "bulk_discount": -100}'::jsonb,
    '["Conseil pharmaceutique", "Programme fidélité", "Livraison rapide"]'::jsonb,
    '["Aucune prescription requise"]'::jsonb,
    false,
    true,
    'pharmacy',
    true,
    '["pharmaceutical_license"]'::jsonb
);

-- Boulangerie & Produits Frais Services
INSERT INTO specialized_services (
    id, category_id, name, description, icon, display_order, base_price, pricing_model,
    subscription_plans, additional_fees, features, requirements, temperature_control,
    partner_type, verification_required
) VALUES
(
    '660e8400-e29b-41d4-a716-446655440005',
    '550e8400-e29b-41d4-a716-446655440003',
    'Pain Frais Quotidien',
    'Livraison quotidienne de pain frais avec abonnement disponible',
    'bread-slice',
    1,
    500.00,
    'fixed',
    '[
        {"name": "Hebdomadaire", "price": 3000, "deliveries": 7, "discount": 15},
        {"name": "Mensuel", "price": 10000, "deliveries": 30, "discount": 25}
    ]'::jsonb,
    '{"early_delivery": 200, "weekend_delivery": 300}'::jsonb,
    '["Pain du jour", "Garantie fraîcheur", "Livraison matinale", "Abonnement flexible"]'::jsonb,
    '["Commande avant 20h pour le lendemain"]'::jsonb,
    false,
    'bakery',
    true
);

-- Pressing & Nettoyage Services
INSERT INTO specialized_services (
    id, category_id, name, description, icon, display_order, base_price, pricing_model,
    additional_fees, features, requirements, pickup_required,
    partner_type, verification_required, appointment_booking
) VALUES
(
    '660e8400-e29b-41d4-a716-446655440006',
    '550e8400-e29b-41d4-a716-446655440004',
    'Nettoyage Standard',
    'Service complet de nettoyage à sec avec collecte et livraison',
    'tshirt-crew',
    1,
    1500.00,
    'fixed',
    '{"express_service": 1000, "special_treatment": 500, "pickup_service": 300}'::jsonb,
    '["Collecte gratuite", "Nettoyage professionnel", "Livraison incluse", "Garantie qualité"]'::jsonb,
    '["Vêtements propres pour collecte"]'::jsonb,
    true,
    'dry_cleaner',
    true,
    true
);

-- Électronique & Technologie Services
INSERT INTO specialized_services (
    id, category_id, name, description, icon, display_order, base_price, pricing_model,
    additional_fees, features, requirements, secure_transport, insurance_coverage, signature_required,
    partner_type, verification_required
) VALUES
(
    '660e8400-e29b-41d4-a716-446655440007',
    '550e8400-e29b-41d4-a716-446655440005',
    'Téléphones & Accessoires',
    'Livraison sécurisée d\'appareils électroniques avec assurance transport',
    'cellphone',
    1,
    2000.00,
    'value_percentage',
    '{"express_delivery": 1500, "insurance_premium": 0, "secure_packaging": 500}'::jsonb,
    '["Transport sécurisé", "Assurance tous risques", "Emballage anti-choc", "Signature obligatoire"]'::jsonb,
    '["Facture d\'achat", "Déclaration de valeur"]'::jsonb,
    true,
    true,
    true,
    'electronics_store',
    true
);

-- Librairie & Papeterie Services
INSERT INTO specialized_services (
    id, category_id, name, description, icon, display_order, base_price, pricing_model,
    additional_fees, features, requirements,
    partner_type, verification_required
) VALUES
(
    '660e8400-e29b-41d4-a716-446655440008',
    '550e8400-e29b-41d4-a716-446655440006',
    'Livraison de Livres',
    'Livraison de livres et fournitures scolaires avec emballage protecteur',
    'book-open-variant',
    1,
    800.00,
    'fixed',
    '{"express_delivery": 500, "bulk_discount": -100, "student_discount": -200}'::jsonb,
    '["Emballage protecteur", "Catalogue étendu", "Livraison soignée", "Réduction étudiants"]'::jsonb,
    '["Liste des articles souhaités"]'::jsonb,
    'bookstore',
    false
);

-- Beauté & Coiffure Services
INSERT INTO specialized_services (
    id, category_id, name, description, icon, display_order, base_price, pricing_model,
    additional_fees, features, requirements, appointment_booking,
    partner_type, verification_required, certification_required
) VALUES
(
    '660e8400-e29b-41d4-a716-446655440009',
    '550e8400-e29b-41d4-a716-446655440007',
    'Coiffure à Domicile',
    'Service de coiffure professionnel à domicile avec équipement inclus',
    'face-woman',
    1,
    8000.00,
    'fixed',
    '{"premium_service": 3000, "group_discount": -1000, "weekend_surcharge": 2000}'::jsonb,
    '["Coiffeur professionnel", "Équipement inclus", "Service personnalisé", "Hygiène garantie"]'::jsonb,
    '["Rendez-vous obligatoire", "Espace approprié"]'::jsonb,
    true,
    'beauty_salon',
    true,
    '["beauty_certification", "hygiene_training"]'::jsonb
);

-- Restaurant & Traiteur Services
INSERT INTO specialized_services (
    id, category_id, name, description, icon, display_order, base_price, pricing_model,
    additional_fees, features, requirements, temperature_control,
    partner_type, verification_required, certification_required
) VALUES
(
    '660e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440008',
    'Garantie Plat Chaud',
    'Livraison de repas avec garantie de température et remboursement si froid',
    'silverware-fork-knife',
    1,
    1200.00,
    'fixed',
    '{"express_delivery": 800, "large_order": 500, "weekend_delivery": 400}'::jsonb,
    '["Température garantie", "Remboursement si froid", "Emballage isotherme", "Livraison rapide"]'::jsonb,
    '["Commande minimum 2000 XOF"]'::jsonb,
    true,
    'restaurant',
    true,
    '["food_license", "hygiene_certification"]'::jsonb
);
