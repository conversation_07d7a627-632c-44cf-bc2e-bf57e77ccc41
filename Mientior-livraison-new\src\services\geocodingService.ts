import * as Location from 'expo-location';

export interface GeocodingResult {
  formatted_address: string;
  street?: string;
  city?: string;
  postal_code?: string;
  country?: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

export interface ReverseGeocodingResult {
  formatted_address: string;
  street?: string;
  city?: string;
  postal_code?: string;
  country?: string;
}

export interface LocationPermissionResult {
  granted: boolean;
  canAskAgain: boolean;
  status: Location.PermissionStatus;
}

class GeocodingService {
  private readonly GOOGLE_MAPS_API_KEY = process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY;

  /**
   * Demande les permissions de localisation
   */
  async requestLocationPermissions(): Promise<LocationPermissionResult> {
    try {
      console.log('📍 Demande des permissions de localisation...');

      // Vérifier les permissions actuelles
      const { status: currentStatus } = await Location.getForegroundPermissionsAsync();
      
      if (currentStatus === 'granted') {
        return {
          granted: true,
          canAskAgain: true,
          status: currentStatus,
        };
      }

      // Demander les permissions
      const { status, canAskAgain } = await Location.requestForegroundPermissionsAsync();
      
      console.log(`📍 Permissions de localisation: ${status}`);
      
      return {
        granted: status === 'granted',
        canAskAgain,
        status,
      };
    } catch (error) {
      console.error('❌ Erreur demande permissions localisation:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: 'denied',
      };
    }
  }

  /**
   * Obtient la position actuelle de l'utilisateur
   */
  async getCurrentLocation(): Promise<{ latitude: number; longitude: number } | null> {
    try {
      console.log('📍 Obtention de la position actuelle...');

      const permissions = await this.requestLocationPermissions();
      if (!permissions.granted) {
        throw new Error('Permissions de localisation non accordées');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeInterval: 10000,
        distanceInterval: 10,
      });

      const { latitude, longitude } = location.coords;
      console.log(`📍 Position obtenue: ${latitude}, ${longitude}`);

      return { latitude, longitude };
    } catch (error) {
      console.error('❌ Erreur obtention position:', error);
      return null;
    }
  }

  /**
   * Géocode une adresse (adresse → coordonnées)
   */
  async geocodeAddress(address: string): Promise<GeocodingResult | null> {
    try {
      console.log('🔍 Géocodage de l\'adresse:', address);

      // Utiliser Expo Location pour le géocodage
      const results = await Location.geocodeAsync(address);
      
      if (results.length === 0) {
        console.warn('⚠️ Aucun résultat de géocodage trouvé');
        return null;
      }

      const result = results[0];
      
      // Obtenir les détails de l'adresse via reverse geocoding
      const reverseResult = await this.reverseGeocode(result.latitude, result.longitude);
      
      return {
        formatted_address: reverseResult?.formatted_address || address,
        street: reverseResult?.street,
        city: reverseResult?.city,
        postal_code: reverseResult?.postal_code,
        country: reverseResult?.country,
        coordinates: {
          latitude: result.latitude,
          longitude: result.longitude,
        },
      };
    } catch (error) {
      console.error('❌ Erreur géocodage:', error);
      return null;
    }
  }

  /**
   * Géocodage inverse (coordonnées → adresse)
   */
  async reverseGeocode(latitude: number, longitude: number): Promise<ReverseGeocodingResult | null> {
    try {
      console.log(`🔍 Géocodage inverse: ${latitude}, ${longitude}`);

      const results = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (results.length === 0) {
        console.warn('⚠️ Aucun résultat de géocodage inverse trouvé');
        return null;
      }

      const result = results[0];
      
      // Construire l'adresse formatée
      const addressParts = [
        result.streetNumber,
        result.street,
        result.city,
        result.region,
        result.postalCode,
        result.country,
      ].filter(Boolean);

      return {
        formatted_address: addressParts.join(', '),
        street: [result.streetNumber, result.street].filter(Boolean).join(' '),
        city: result.city || undefined,
        postal_code: result.postalCode || undefined,
        country: result.country || undefined,
      };
    } catch (error) {
      console.error('❌ Erreur géocodage inverse:', error);
      return null;
    }
  }

  /**
   * Recherche d'adresses avec autocomplétion
   */
  async searchAddresses(query: string): Promise<GeocodingResult[]> {
    try {
      console.log('🔍 Recherche d\'adresses:', query);

      if (!query || query.trim().length < 3) {
        return [];
      }

      // Utiliser le géocodage pour la recherche
      const results = await Location.geocodeAsync(query);
      
      const geocodingResults: GeocodingResult[] = [];
      
      for (const result of results.slice(0, 5)) { // Limiter à 5 résultats
        const reverseResult = await this.reverseGeocode(result.latitude, result.longitude);
        
        if (reverseResult) {
          geocodingResults.push({
            formatted_address: reverseResult.formatted_address,
            street: reverseResult.street,
            city: reverseResult.city,
            postal_code: reverseResult.postal_code,
            country: reverseResult.country,
            coordinates: {
              latitude: result.latitude,
              longitude: result.longitude,
            },
          });
        }
      }

      return geocodingResults;
    } catch (error) {
      console.error('❌ Erreur recherche adresses:', error);
      return [];
    }
  }

  /**
   * Valide une adresse
   */
  async validateAddress(address: string): Promise<{ isValid: boolean; suggestion?: GeocodingResult }> {
    try {
      const result = await this.geocodeAddress(address);
      
      if (!result) {
        return { isValid: false };
      }

      return {
        isValid: true,
        suggestion: result,
      };
    } catch (error) {
      console.error('❌ Erreur validation adresse:', error);
      return { isValid: false };
    }
  }

  /**
   * Calcule la distance entre deux points
   */
  calculateDistance(
    point1: { latitude: number; longitude: number },
    point2: { latitude: number; longitude: number }
  ): number {
    const R = 6371; // Rayon de la Terre en km
    const dLat = this.toRad(point2.latitude - point1.latitude);
    const dLon = this.toRad(point2.longitude - point1.longitude);
    const lat1 = this.toRad(point1.latitude);
    const lat2 = this.toRad(point2.latitude);

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c;

    return d;
  }

  private toRad(value: number): number {
    return (value * Math.PI) / 180;
  }

  /**
   * Formate une adresse pour l'affichage
   */
  formatAddressForDisplay(result: GeocodingResult | ReverseGeocodingResult): string {
    return result.formatted_address;
  }

  /**
   * Vérifie si les services de localisation sont activés
   */
  async isLocationServicesEnabled(): Promise<boolean> {
    try {
      const enabled = await Location.hasServicesEnabledAsync();
      console.log('📍 Services de localisation activés:', enabled);
      return enabled;
    } catch (error) {
      console.error('❌ Erreur vérification services localisation:', error);
      return false;
    }
  }

  /**
   * Obtient la région visible pour une liste de coordonnées
   */
  getRegionForCoordinates(coordinates: { latitude: number; longitude: number }[]): {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  } {
    if (coordinates.length === 0) {
      // Région par défaut (Abidjan, Côte d'Ivoire)
      return {
        latitude: 5.3364,
        longitude: -4.0267,
        latitudeDelta: 0.1,
        longitudeDelta: 0.1,
      };
    }

    if (coordinates.length === 1) {
      return {
        latitude: coordinates[0].latitude,
        longitude: coordinates[0].longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
    }

    const latitudes = coordinates.map(coord => coord.latitude);
    const longitudes = coordinates.map(coord => coord.longitude);

    const minLat = Math.min(...latitudes);
    const maxLat = Math.max(...latitudes);
    const minLng = Math.min(...longitudes);
    const maxLng = Math.max(...longitudes);

    const centerLat = (minLat + maxLat) / 2;
    const centerLng = (minLng + maxLng) / 2;
    const latDelta = (maxLat - minLat) * 1.2; // Ajouter une marge
    const lngDelta = (maxLng - minLng) * 1.2;

    return {
      latitude: centerLat,
      longitude: centerLng,
      latitudeDelta: Math.max(latDelta, 0.01),
      longitudeDelta: Math.max(lngDelta, 0.01),
    };
  }
}

export const geocodingService = new GeocodingService();
export default geocodingService;
