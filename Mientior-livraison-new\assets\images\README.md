# 🖼️ Mientior Livraison - Image Assets

This directory contains all image assets for the Mientior Livraison React Native Expo application, following African design aesthetics and royalty-free licensing requirements.

## 📁 **Directory Structure**

```
assets/images/
├── logo/                    # App branding and logos
├── onboarding/             # Onboarding carousel images
├── ui/                     # UI elements and backgrounds
├── categories/             # Service category images
├── placeholders/           # Fallback and placeholder images
├── delivery/               # Delivery-related imagery
├── location/               # Location and map elements
├── empty-states/           # Empty state illustrations
├── states/                 # Status icons (success, error, loading)
├── IMAGE_ATTRIBUTIONS.md   # Detailed licensing information
└── README.md              # This file
```

## 🎨 **Design Guidelines**

### **African Design Aesthetic**
- **Primary Color:** #0DCAA8 (African green)
- **Border Radius:** 16px for consistency
- **Cultural Appropriateness:** All images suitable for African markets
- **Color Harmony:** Complementary colors that work with #0DCAA8

### **Mobile Optimization**
- **Maximum File Size:** 1MB per image
- **Formats:** WebP (preferred), PNG, JPG
- **Resolutions:** @1x, @2x, @3x for React Native
- **Compression:** Optimized for mobile performance

## 🔧 **Usage Instructions**

### **Importing Images**
```typescript
// Import from centralized index
import { Images } from '../../assets/images';

// Use in components
<Image source={Images.AppLogo} style={styles.logo} />

// For placeholders, use base64 versions
import { PlaceholderImages } from '../../assets/images/placeholders';
<Image source={{ uri: PlaceholderImages.RESTAURANT_PLACEHOLDER_BASE64 }} />
```

### **Vector Icons**
```typescript
// Use @expo/vector-icons with African color scheme
import { Ionicons } from '@expo/vector-icons';

<Ionicons 
  name="location" 
  size={24} 
  color="#0DCAA8" 
  accessibilityLabel="Location icon"
/>
```

## 📱 **Responsive Design**

### **Image Sizing Guidelines**
- **App Logo:** 120x120px (@1x), 240x240px (@2x), 360x360px (@3x)
- **Category Images:** 80x80px (@1x), 160x160px (@2x), 240x240px (@3x)
- **Restaurant Cards:** 300x200px (@1x), 600x400px (@2x), 900x600px (@3x)
- **Onboarding:** 200x200px (@1x), 400x400px (@2x), 600x600px (@3x)

### **Lazy Loading Implementation**
```typescript
// Implement lazy loading for performance
import { Image } from 'react-native';

const LazyImage = ({ source, style, ...props }) => (
  <Image
    source={source}
    style={style}
    resizeMode="cover"
    loadingIndicatorSource={PlaceholderImages.LOADING_PLACEHOLDER}
    {...props}
  />
);
```

## ♿ **Accessibility Requirements**

### **Alt Text Guidelines**
```typescript
// Always provide meaningful accessibility labels
<Image 
  source={Images.RestaurantCategory}
  style={styles.categoryImage}
  accessibilityLabel="Restaurant category - African cuisine and dining"
/>
```

### **Screen Reader Support**
- Descriptive alt text for all images
- Meaningful labels for decorative images
- High contrast ratios maintained
- Focus indicators for interactive images

## 🔄 **Placeholder System**

### **Base64 Placeholders**
For immediate loading and offline support:
```typescript
// Available base64 placeholders
PlaceholderImages.APP_LOGO_BASE64
PlaceholderImages.FOOD_DELIVERY_BASE64
PlaceholderImages.RESTAURANT_PLACEHOLDER_BASE64
PlaceholderImages.DEFAULT_AVATAR_BASE64
PlaceholderImages.LOCATION_BACKGROUND_BASE64
```

### **Fallback Strategy**
```typescript
// Implement robust fallback system
const ImageWithFallback = ({ source, fallback, ...props }) => {
  const [imageError, setImageError] = useState(false);
  
  return (
    <Image
      source={imageError ? { uri: fallback } : source}
      onError={() => setImageError(true)}
      {...props}
    />
  );
};
```

## 🌍 **Cultural Considerations**

### **African Market Appropriateness**
- Food imagery featuring African cuisine
- Diverse representation in people imagery
- Local architectural and landscape elements
- Culturally sensitive color choices

### **Localization Support**
- Text-free images for easy localization
- Universal symbols and icons
- Cultural context awareness
- Regional adaptation capability

## 🚀 **Performance Optimization**

### **Image Optimization Checklist**
- ✅ Compressed for mobile
- ✅ Multiple resolution variants
- ✅ WebP format when possible
- ✅ Lazy loading implemented
- ✅ Caching strategy in place
- ✅ Fallback system active

### **Bundle Size Management**
```typescript
// Use dynamic imports for large images
const loadLargeImage = async () => {
  const image = await import('./large-image.png');
  return image.default;
};
```

## 🔒 **Licensing Compliance**

### **Royalty-Free Sources**
- **Unsplash:** CC0 License (Public Domain)
- **Pexels:** Free License
- **Pixabay:** Pixabay License
- **Original Designs:** Created for Mientior Livraison

### **Attribution Requirements**
- Most images require no attribution (CC0, Pexels Free, Pixabay)
- Original designs owned by Mientior Livraison
- Full attribution details in `IMAGE_ATTRIBUTIONS.md`

## 🛠️ **Development Workflow**

### **Adding New Images**
1. **Source Selection:** Use only royalty-free sources
2. **Optimization:** Compress and resize for mobile
3. **Multiple Resolutions:** Create @1x, @2x, @3x variants
4. **Update Index:** Add to `assets/images/index.ts`
5. **Documentation:** Update attribution file
6. **Testing:** Verify on multiple devices

### **Image Replacement Process**
1. **Backup Original:** Keep copy of replaced image
2. **Update References:** Search and replace all usages
3. **Test Thoroughly:** Verify all screens and components
4. **Update Documentation:** Modify attribution records
5. **Performance Check:** Ensure no degradation

## 🧪 **Testing Guidelines**

### **Visual Testing Checklist**
- ✅ Images load correctly on all devices
- ✅ Proper scaling on different screen sizes
- ✅ Fallbacks work when images fail to load
- ✅ Accessibility labels are meaningful
- ✅ Performance impact is minimal
- ✅ Cultural appropriateness verified

### **Automated Testing**
```typescript
// Test image loading
describe('Image Loading', () => {
  it('should load app logo successfully', () => {
    const { getByTestId } = render(<AppLogo testID="app-logo" />);
    expect(getByTestId('app-logo')).toBeTruthy();
  });
});
```

## 📊 **Monitoring and Analytics**

### **Image Performance Metrics**
- Load times per image category
- Fallback usage frequency
- User engagement with visual content
- Accessibility feature usage

### **Optimization Opportunities**
- Regular compression audits
- Format migration (PNG → WebP)
- Unused image cleanup
- Cache hit rate optimization

---

## 🔗 **Related Documentation**

- **Design System:** `src/constants/theme.ts`
- **Component Library:** `src/components/`
- **Accessibility Guide:** `docs/ACCESSIBILITY.md`
- **Performance Guide:** `docs/PERFORMANCE.md`

---

**Maintained By:** Augment Agent  
**Last Updated:** 2025-06-15  
**Version:** 1.0.0
