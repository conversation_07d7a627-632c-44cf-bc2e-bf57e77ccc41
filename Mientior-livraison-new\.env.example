# 🔐 Environment Variables - <PERSON><PERSON><PERSON>
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# ==========================================
# 🗄️ SUPABASE CONFIGURATION
# ==========================================

# Google APIs Configuration
# Obtenez vos clés API sur https://console.cloud.google.com/
EXPO_PUBLIC_GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
EXPO_PUBLIC_GOOGLE_DISTANCE_MATRIX_API_KEY=your_google_distance_matrix_api_key_here

# Development Environment
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here

# Production Environment (use different project for production)
PRODUCTION_SUPABASE_URL=https://your-production-project.supabase.co
PRODUCTION_SUPABASE_ANON_KEY=your_production_anon_key_here

# Staging Environment
STAGING_SUPABASE_URL=https://your-staging-project.supabase.co
STAGING_SUPABASE_ANON_KEY=your_staging_anon_key_here

# Service Role Keys (for server-side operations only)
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
PRODUCTION_SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key_here

# ==========================================
# 🗺️ GOOGLE MAPS CONFIGURATION
# ==========================================

# Development
GOOGLE_MAPS_API_KEY=your_development_maps_api_key

# Production
PRODUCTION_GOOGLE_MAPS_API_KEY=your_production_maps_api_key

# Staging
STAGING_GOOGLE_MAPS_API_KEY=your_staging_maps_api_key

# ==========================================
# 💳 AFRICAN PAYMENT PROVIDERS
# ==========================================

# Orange Money
ORANGE_MONEY_API_KEY=your_orange_money_api_key
ORANGE_MONEY_MERCHANT_ID=your_orange_merchant_id
ORANGE_MONEY_SECRET_KEY=your_orange_secret_key
ORANGE_MONEY_BASE_URL=https://api.orange.com/orange-money-webpay/dev/v1

# MTN Mobile Money
MTN_MONEY_API_KEY=your_mtn_money_api_key
MTN_MONEY_USER_ID=your_mtn_user_id
MTN_MONEY_SUBSCRIPTION_KEY=your_mtn_subscription_key
MTN_MONEY_BASE_URL=https://sandbox.momodeveloper.mtn.com

# Wave
WAVE_API_KEY=your_wave_api_key
WAVE_SECRET_KEY=your_wave_secret_key
WAVE_BASE_URL=https://api.wave.com/v1

# Moov Money
MOOV_MONEY_API_KEY=your_moov_money_api_key
MOOV_MONEY_MERCHANT_ID=your_moov_merchant_id
MOOV_MONEY_SECRET_KEY=your_moov_secret_key

# ==========================================
# 🔔 PUSH NOTIFICATIONS
# ==========================================

# Expo Push Notifications
EXPO_ACCESS_TOKEN=your_expo_access_token

# Firebase (for advanced notifications)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# ==========================================
# 📊 ANALYTICS & MONITORING
# ==========================================

# Sentry (Error Tracking)
SENTRY_DSN=your_sentry_dsn
SENTRY_AUTH_TOKEN=your_sentry_auth_token

# Firebase Analytics
FIREBASE_APP_ID=your_firebase_app_id
FIREBASE_MEASUREMENT_ID=your_firebase_measurement_id

# ==========================================
# 🔐 APP STORE CREDENTIALS
# ==========================================

# Apple App Store
APPLE_ID=<EMAIL>
APPLE_TEAM_ID=your_apple_team_id
ASC_APP_ID=your_app_store_connect_app_id

# Google Play Store
GOOGLE_SERVICE_ACCOUNT_KEY_PATH=../path/to/google-service-account.json
GOOGLE_PLAY_TRACK=internal

# ==========================================
# 🏗️ BUILD CONFIGURATION
# ==========================================

# EAS Build
EAS_PROJECT_ID=your_eas_project_id

# Environment
NODE_ENV=development
EXPO_PUBLIC_ENV=development

# ==========================================
# 🌍 LOCALIZATION
# ==========================================

# Default Language
DEFAULT_LANGUAGE=fr
SUPPORTED_LANGUAGES=fr,en

# Currency
DEFAULT_CURRENCY=XOF
CURRENCY_SYMBOL=CFA

# ==========================================
# 🚀 DEPLOYMENT
# ==========================================

# App Version
APP_VERSION=1.0.0
BUILD_NUMBER=1

# Release Channel
RELEASE_CHANNEL=default

# ==========================================
# 🔧 DEVELOPMENT TOOLS
# ==========================================

# Debug Mode
DEBUG=true
EXPO_DEBUG=true

# Metro Bundler
METRO_PORT=8081

# Development Server
DEV_SERVER_PORT=19000

# ==========================================
# 📱 DEVICE TESTING
# ==========================================

# Test Device IDs (for internal testing)
TEST_DEVICE_IDS=device_id_1,device_id_2

# Beta Testing
TESTFLIGHT_GROUPS=internal-testers,beta-testers
GOOGLE_PLAY_INTERNAL_TESTING=true

# ==========================================
# 🔒 SECURITY
# ==========================================

# JWT Secret (for custom auth if needed)
JWT_SECRET=your_jwt_secret_key

# Encryption Keys
ENCRYPTION_KEY=your_encryption_key

# API Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# ==========================================
# 📧 COMMUNICATION
# ==========================================

# Email Service (for notifications)
SENDGRID_API_KEY=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>

# SMS Service
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# ==========================================
# 🗄️ DATABASE BACKUP
# ==========================================

# Backup Configuration
BACKUP_FREQUENCY=daily
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_BUCKET=your_backup_bucket

# ==========================================
# 📈 PERFORMANCE MONITORING
# ==========================================

# Performance Monitoring
PERFORMANCE_MONITORING_ENABLED=true
CRASH_REPORTING_ENABLED=true

# ==========================================
# 🌐 CDN & ASSETS
# ==========================================

# CDN Configuration
CDN_BASE_URL=https://cdn.mientior.com
ASSETS_BASE_URL=https://assets.mientior.com

# Image Optimization
IMAGE_OPTIMIZATION_ENABLED=true
IMAGE_QUALITY=80

# ==========================================
# 🔄 FEATURE FLAGS
# ==========================================

# Feature Toggles
FEATURE_VOICE_SEARCH=true
FEATURE_REAL_TIME_TRACKING=true
FEATURE_ADVANCED_ANALYTICS=false
FEATURE_BETA_FEATURES=false
