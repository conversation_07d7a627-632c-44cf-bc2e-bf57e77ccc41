-- Tables pour les fonctionnalités avancées de l'application Livraison Afrique
-- À exécuter dans Supabase SQL Editor

-- 1. Table pour les notifications push
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  titre TEXT NOT NULL,
  contenu TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('order', 'delivery', 'promotion', 'system')),
  data_json JSONB DEFAULT '{}',
  is_lu BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_is_lu ON notifications(is_lu);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);

-- 2. Table pour les messages de chat
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_id UUID,
  delivery_id UUID REFERENCES livraisons(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  type TEXT DEFAULT 'text' CHECK (type IN ('text', 'image', 'location', 'system')),
  metadata JSONB DEFAULT '{}',
  read_by UUID[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour optimiser les requêtes de chat
CREATE INDEX IF NOT EXISTS idx_chat_messages_chat_id ON chat_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_delivery_id ON chat_messages(delivery_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sender_id ON chat_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);

-- 3. Table pour les participants de chat
CREATE TABLE IF NOT EXISTS chat_participants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_id UUID,
  delivery_id UUID REFERENCES livraisons(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('client', 'livreur', 'marchand', 'support')),
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(chat_id, user_id),
  UNIQUE(delivery_id, user_id)
);

-- Index pour les participants
CREATE INDEX IF NOT EXISTS idx_chat_participants_chat_id ON chat_participants(chat_id);
CREATE INDEX IF NOT EXISTS idx_chat_participants_delivery_id ON chat_participants(delivery_id);
CREATE INDEX IF NOT EXISTS idx_chat_participants_user_id ON chat_participants(user_id);

-- 4. Table pour les événements analytics
CREATE TABLE IF NOT EXISTS analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  event_name TEXT NOT NULL,
  properties JSONB DEFAULT '{}',
  session_id TEXT,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les analytics
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_name ON analytics_events(event_name);
CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON analytics_events(session_id);

-- 5. Table pour les tokens de push notifications
CREATE TABLE IF NOT EXISTS push_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  token TEXT NOT NULL,
  platform TEXT NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, token)
);

-- Index pour les tokens
CREATE INDEX IF NOT EXISTS idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_push_tokens_is_active ON push_tokens(is_active);

-- 6. Table pour l'historique de tracking
CREATE TABLE IF NOT EXISTS tracking_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  delivery_id UUID REFERENCES livraisons(id) ON DELETE CASCADE,
  status TEXT NOT NULL,
  location JSONB,
  message TEXT,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour l'historique de tracking
CREATE INDEX IF NOT EXISTS idx_tracking_history_delivery_id ON tracking_history(delivery_id);
CREATE INDEX IF NOT EXISTS idx_tracking_history_timestamp ON tracking_history(timestamp DESC);

-- 7. Mise à jour de la table livraisons pour le tracking
ALTER TABLE livraisons 
ADD COLUMN IF NOT EXISTS position_livreur_lat DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS position_livreur_lng DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS estimated_arrival TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS distance_remaining DECIMAL(10, 2),
ADD COLUMN IF NOT EXISTS tracking_active BOOLEAN DEFAULT FALSE;

-- 8. Mise à jour de la table users pour les analytics
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS push_token TEXT,
ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{"orders": true, "delivery": true, "promotions": true, "system": true}';

-- 9. Fonctions pour les triggers de mise à jour
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 10. Triggers pour les mises à jour automatiques
CREATE TRIGGER update_notifications_updated_at 
    BEFORE UPDATE ON notifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chat_messages_updated_at 
    BEFORE UPDATE ON chat_messages 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_push_tokens_updated_at 
    BEFORE UPDATE ON push_tokens 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 11. Politiques RLS (Row Level Security)
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE tracking_history ENABLE ROW LEVEL SECURITY;

-- Politiques pour les notifications
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Politiques pour les messages de chat
CREATE POLICY "Users can view messages in their chats" ON chat_messages
    FOR SELECT USING (
        sender_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM chat_participants 
            WHERE (chat_id = chat_messages.chat_id OR delivery_id = chat_messages.delivery_id)
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can send messages" ON chat_messages
    FOR INSERT WITH CHECK (sender_id = auth.uid());

-- Politiques pour les participants de chat
CREATE POLICY "Users can view chat participants" ON chat_participants
    FOR SELECT USING (user_id = auth.uid());

-- Politiques pour les analytics
CREATE POLICY "Users can create their own analytics events" ON analytics_events
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Politiques pour les tokens push
CREATE POLICY "Users can manage their own push tokens" ON push_tokens
    FOR ALL USING (user_id = auth.uid());

-- Politiques pour l'historique de tracking
CREATE POLICY "Users can view tracking history for their deliveries" ON tracking_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM livraisons 
            WHERE id = tracking_history.delivery_id 
            AND (client_id = auth.uid() OR livreur_id = auth.uid())
        )
    );

-- 12. Fonctions utilitaires pour les analytics
CREATE OR REPLACE FUNCTION get_user_analytics(
    p_user_id UUID,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_orders', COUNT(DISTINCT c.id),
        'total_spent', COALESCE(SUM(c.montant_total), 0),
        'average_order_value', COALESCE(AVG(c.montant_total), 0),
        'favorite_restaurants', (
            SELECT json_agg(json_build_object('name', mp.nom, 'orders', order_count))
            FROM (
                SELECT c.merchant_id, COUNT(*) as order_count
                FROM commandes c
                WHERE c.client_id = p_user_id 
                AND c.created_at BETWEEN p_start_date AND p_end_date
                GROUP BY c.merchant_id
                ORDER BY order_count DESC
                LIMIT 5
            ) top_merchants
            JOIN merchant_profiles mp ON mp.id = top_merchants.merchant_id
        )
    ) INTO result
    FROM commandes c
    WHERE c.client_id = p_user_id 
    AND c.created_at BETWEEN p_start_date AND p_end_date;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. Fonction pour nettoyer les anciennes données
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Supprimer les anciens événements analytics (plus de 1 an)
    DELETE FROM analytics_events 
    WHERE timestamp < NOW() - INTERVAL '1 year';
    
    -- Supprimer les anciens messages de chat (plus de 6 mois)
    DELETE FROM chat_messages 
    WHERE created_at < NOW() - INTERVAL '6 months';
    
    -- Supprimer les anciennes notifications lues (plus de 3 mois)
    DELETE FROM notifications 
    WHERE is_lu = true AND created_at < NOW() - INTERVAL '3 months';
    
    -- Désactiver les anciens tokens push (plus de 6 mois sans utilisation)
    UPDATE push_tokens 
    SET is_active = false 
    WHERE updated_at < NOW() - INTERVAL '6 months';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 14. Créer un job pour le nettoyage automatique (à configurer dans Supabase)
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * 0', 'SELECT cleanup_old_data();');

COMMENT ON TABLE notifications IS 'Table pour stocker les notifications push et locales';
COMMENT ON TABLE chat_messages IS 'Table pour les messages de chat temps réel';
COMMENT ON TABLE chat_participants IS 'Table pour les participants des chats';
COMMENT ON TABLE analytics_events IS 'Table pour les événements analytics et tracking';
COMMENT ON TABLE push_tokens IS 'Table pour les tokens de notifications push';
COMMENT ON TABLE tracking_history IS 'Table pour l''historique de tracking des livraisons';
