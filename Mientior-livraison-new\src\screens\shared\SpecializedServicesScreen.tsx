import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

import { colors, typography, spacing } from '../../constants/theme';
import { SPECIALIZED_SERVICE_CATEGORIES, SpecializedServiceCategory, SpecializedService } from '../../types/specializedServices';
import { CustomButton } from '../../components/CustomButton';

const { width } = Dimensions.get('window');

const SpecializedServicesScreen: React.FC = () => {
  const navigation = useNavigation();
  const [selectedCategory, setSelectedCategory] = useState<string>('colis_merchandise');
  const [selectedService, setSelectedService] = useState<SpecializedService | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const currentCategory = SPECIALIZED_SERVICE_CATEGORIES.find(cat => cat.id === selectedCategory);

  const onRefresh = () => {
    setRefreshing(true);
    // Simuler le rechargement des données
    setTimeout(() => setRefreshing(false), 1000);
  };

  const renderCategoryTab = (category: SpecializedServiceCategory) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryTab,
        selectedCategory === category.id && styles.categoryTabActive,
      ]}
      onPress={() => setSelectedCategory(category.id)}
    >
      <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
        <Ionicons name={category.icon as any} size={20} color={category.color} />
      </View>
      <Text style={[
        styles.categoryTabText,
        selectedCategory === category.id && styles.categoryTabTextActive,
      ]}>
        {category.name}
      </Text>
      <View style={[styles.valueIndicator, { backgroundColor: getValueColor(category.customerValue) }]}>
        <Text style={styles.valueText}>{category.valueProposition}</Text>
      </View>
    </TouchableOpacity>
  );

  const getValueColor = (customerValue: string): string => {
    switch (customerValue) {
      case 'premium': return colors.success;
      case 'standard': return colors.primary[500];
      case 'basic': return colors.neutral[400];
      default: return colors.primary[500];
    }
  };

  const renderServiceCard = (service: SpecializedService) => (
    <TouchableOpacity
      key={service.id}
      style={styles.serviceCard}
      onPress={() => setSelectedService(service)}
    >
      <View style={styles.serviceHeader}>
        <View style={[styles.serviceIcon, { backgroundColor: currentCategory?.color + '20' }]}>
          <Ionicons name={service.icon as any} size={24} color={currentCategory?.color} />
        </View>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceName}>{service.name}</Text>
          <Text style={styles.serviceDescription}>{service.description}</Text>
        </View>
        <View style={styles.servicePricing}>
          <Text style={styles.priceAmount}>
            {service.pricing.basePrice.toLocaleString()} {service.pricing.currency}
          </Text>
          <Text style={styles.pricingModel}>
            {getPricingModelText(service.pricing.pricingModel)}
          </Text>
        </View>
      </View>

      <View style={styles.serviceFeatures}>
        {service.features.slice(0, 3).map((feature, index) => (
          <View key={index} style={styles.featureTag}>
            <Ionicons name="checkmark-circle" size={12} color={colors.success} />
            <Text style={styles.featureText}>{feature}</Text>
          </View>
        ))}
      </View>

      <View style={styles.serviceMetrics}>
        <View style={styles.metricItem}>
          <Ionicons name="time" size={14} color={colors.text.secondary} />
          <Text style={styles.metricText}>
            {service.availability.minDeliveryTime}-{service.availability.maxDeliveryTime} min
          </Text>
        </View>
        <View style={styles.metricItem}>
          <Ionicons name="location" size={14} color={colors.text.secondary} />
          <Text style={styles.metricText}>
            {service.availability.cities.length} ville{service.availability.cities.length > 1 ? 's' : ''}
          </Text>
        </View>
        <View style={styles.metricItem}>
          <Ionicons name="shield-checkmark" size={14} color={colors.primary[500]} />
          <Text style={styles.metricText}>
            {service.specialHandling.insuranceCoverage ? 'Assuré' : 'Standard'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const getPricingModelText = (model: string): string => {
    switch (model) {
      case 'fixed': return 'Prix fixe';
      case 'weight': return 'Par poids';
      case 'size': return 'Par taille';
      case 'distance': return 'Par distance';
      case 'time': return 'Par temps';
      case 'value_percentage': return 'Par valeur';
      case 'subscription': return 'Abonnement';
      default: return 'Variable';
    }
  };

  const handleOrderService = (service: SpecializedService) => {
    Alert.alert(
      'Commander ce service',
      `Voulez-vous commander "${service.name}" ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Commander', 
          onPress: () => {
            navigation.navigate('ServiceOrderForm' as never, { 
              service,
              category: currentCategory 
            });
          }
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Services Spécialisés</Text>
          <Text style={styles.headerSubtitle}>Livraison professionnelle adaptée</Text>
        </View>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Category Tabs */}
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Catégories de Services</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {SPECIALIZED_SERVICE_CATEGORIES.map(renderCategoryTab)}
          </ScrollView>
        </View>

        {/* Category Info */}
        {currentCategory && (
          <View style={styles.categoryInfoCard}>
            <View style={styles.categoryInfoHeader}>
              <View style={[styles.categoryInfoIcon, { backgroundColor: currentCategory.color + '20' }]}>
                <Ionicons name={currentCategory.icon as any} size={32} color={currentCategory.color} />
              </View>
              <View style={styles.categoryInfoContent}>
                <Text style={styles.categoryInfoName}>{currentCategory.name}</Text>
                <Text style={styles.categoryInfoDescription}>{currentCategory.description}</Text>
              </View>
              <View style={styles.categoryInfoMetrics}>
                <Text style={styles.valueText}>{currentCategory.customerSatisfaction}%</Text>
                <Text style={styles.valueLabel}>Satisfaction</Text>
              </View>
            </View>

            {/* Special Requirements */}
            <View style={styles.requirementsSection}>
              <Text style={styles.requirementsTitle}>Exigences Spéciales</Text>
              <View style={styles.requirementsList}>
                {currentCategory.specialRequirements.slice(0, 2).map((req, index) => (
                  <View key={index} style={styles.requirementItem}>
                    <Ionicons name="checkmark-circle" size={14} color={colors.warning} />
                    <Text style={styles.requirementText}>{req}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        )}

        {/* Services List */}
        {currentCategory && (
          <View style={styles.servicesSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Services Disponibles</Text>
              <View style={styles.servicesBadge}>
                <Text style={styles.servicesBadgeText}>
                  {currentCategory.services.length} service{currentCategory.services.length > 1 ? 's' : ''}
                </Text>
              </View>
            </View>
            
            <View style={styles.servicesContainer}>
              {currentCategory.services.map(renderServiceCard)}
            </View>
          </View>
        )}

        {/* Service Details Modal */}
        {selectedService && (
          <View style={styles.serviceDetailsSection}>
            <Text style={styles.sectionTitle}>Détails du Service</Text>
            <View style={styles.serviceDetailsCard}>
              <View style={styles.serviceDetailsHeader}>
                <Text style={styles.serviceDetailsName}>{selectedService.name}</Text>
                <TouchableOpacity 
                  onPress={() => setSelectedService(null)}
                  style={styles.closeButton}
                >
                  <Ionicons name="close" size={20} color={colors.text.secondary} />
                </TouchableOpacity>
              </View>

              <Text style={styles.serviceDetailsDescription}>
                {selectedService.description}
              </Text>

              {/* Pricing Details */}
              <View style={styles.pricingSection}>
                <Text style={styles.pricingSectionTitle}>Tarification</Text>
                <View style={styles.pricingGrid}>
                  <View style={styles.pricingItem}>
                    <Text style={styles.pricingLabel}>Prix de base</Text>
                    <Text style={styles.pricingValue}>
                      {selectedService.pricing.basePrice.toLocaleString()} {selectedService.pricing.currency}
                    </Text>
                  </View>
                  <View style={styles.pricingItem}>
                    <Text style={styles.pricingLabel}>Modèle</Text>
                    <Text style={styles.pricingValue}>
                      {getPricingModelText(selectedService.pricing.pricingModel)}
                    </Text>
                  </View>
                </View>

                {/* Additional Fees */}
                {Object.keys(selectedService.pricing.additionalFees).length > 0 && (
                  <View style={styles.additionalFeesSection}>
                    <Text style={styles.additionalFeesTitle}>Frais additionnels</Text>
                    {Object.entries(selectedService.pricing.additionalFees).map(([key, value]) => (
                      <View key={key} style={styles.feeItem}>
                        <Text style={styles.feeLabel}>{getFeeLabel(key)}</Text>
                        <Text style={styles.feeValue}>+{value} XOF</Text>
                      </View>
                    ))}
                  </View>
                )}
              </View>

              {/* Features */}
              <View style={styles.featuresSection}>
                <Text style={styles.featuresTitle}>Fonctionnalités incluses</Text>
                {selectedService.features.map((feature, index) => (
                  <View key={index} style={styles.featureItem}>
                    <Ionicons name="checkmark-circle" size={16} color={colors.success} />
                    <Text style={styles.featureItemText}>{feature}</Text>
                  </View>
                ))}
              </View>

              <CustomButton
                title="Commander ce service"
                onPress={() => handleOrderService(selectedService)}
                gradient
                leftIcon="bag-add"
              />
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const getFeeLabel = (key: string): string => {
  const labels: Record<string, string> = {
    expressDelivery: 'Livraison express',
    fragileHandling: 'Manipulation fragile',
    insurance: 'Assurance',
    temperatureControl: 'Contrôle température',
    specialPackaging: 'Emballage spécial',
    emergencyService: 'Service d\'urgence',
    pickupService: 'Service de collecte',
    customization: 'Personnalisation',
  };
  return labels[key] || key;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  content: {
    flex: 1,
  },
  categoriesSection: {
    paddingVertical: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.md,
    paddingHorizontal: spacing.md,
  },
  categoriesContainer: {
    paddingHorizontal: spacing.md,
    gap: spacing.sm,
  },
  categoryTab: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    minWidth: 120,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  categoryTabActive: {
    backgroundColor: colors.primary[100],
    borderColor: colors.primary[300],
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  categoryTabText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  categoryTabTextActive: {
    color: colors.primary[700],
  },
  valueIndicator: {
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
  },
  valueText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.inverse,
  },
  categoryInfoCard: {
    backgroundColor: colors.background.secondary,
    marginHorizontal: spacing.md,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  categoryInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  categoryInfoIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  categoryInfoContent: {
    flex: 1,
  },
  categoryInfoName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: 4,
  },
  categoryInfoDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 20,
  },
  categoryInfoMetrics: {
    alignItems: 'center',
  },
  valueLabel: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  requirementsSection: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.neutral[200],
  },
  requirementsTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  requirementsList: {
    gap: spacing.xs,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  requirementText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    flex: 1,
  },
  servicesSection: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  servicesBadge: {
    backgroundColor: colors.primary[100],
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  servicesBadgeText: {
    fontSize: typography.fontSize.xs,
    color: colors.primary[700],
    fontWeight: typography.fontWeight.medium,
  },
  servicesContainer: {
    gap: spacing.md,
  },
  serviceCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  serviceIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 18,
  },
  servicePricing: {
    alignItems: 'flex-end',
  },
  priceAmount: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  pricingModel: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  serviceFeatures: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
    marginBottom: spacing.sm,
  },
  featureTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.success + '10',
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 8,
    gap: 4,
  },
  featureText: {
    fontSize: typography.fontSize.xs,
    color: colors.success,
  },
  serviceMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metricText: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  serviceDetailsSection: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
  },
  serviceDetailsCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  serviceDetailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  serviceDetailsName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    flex: 1,
  },
  closeButton: {
    padding: spacing.xs,
  },
  serviceDetailsDescription: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    lineHeight: 22,
    marginBottom: spacing.lg,
  },
  pricingSection: {
    marginBottom: spacing.lg,
  },
  pricingSectionTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  pricingGrid: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  pricingItem: {
    flex: 1,
  },
  pricingLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: 2,
  },
  pricingValue: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  additionalFeesSection: {
    backgroundColor: colors.neutral[50],
    borderRadius: 8,
    padding: spacing.md,
  },
  additionalFeesTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  feeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  feeLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  feeValue: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.warning,
  },
  featuresSection: {
    marginBottom: spacing.lg,
  },
  featuresTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.xs,
  },
  featureItemText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    flex: 1,
  },
});

export default SpecializedServicesScreen;
