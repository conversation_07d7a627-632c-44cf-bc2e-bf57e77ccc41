# 🚀 GUIDE DE BUILD ET TEST - SERVICES GÉNÉRATEURS DE REVENUS

## 📋 **PRÉREQUIS**

### **1. Outils Requis**
```bash
# Vérifier les installations
node --version          # v18+ requis
npm --version           # v9+ requis
expo --version          # v50+ requis
eas --version           # v7+ requis
```

### **2. Configuration Environnement**
```bash
# Se connecter à Expo
expo login

# Se connecter à EAS
eas login

# Vérifier la configuration du projet
eas project:info
```

## 🔧 **ÉTAPES DE BUILD**

### **Étape 1 : Préparation du Build**
```bash
# Naviguer vers le projet
cd "Mientior-livraison-new"

# Installer les dépendances
npm install

# Nettoyer le cache
expo r -c
```

### **Étape 2 : Configuration Variables d'Environnement**
Créer un fichier `.env.local` :
```env
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://hlvstikqlbyhofkkgrac.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=votre_anon_key_ici

# Google Maps API
GOOGLE_MAPS_API_KEY=votre_google_maps_key_ici

# Environment
EXPO_PUBLIC_ENV=development
NODE_ENV=development
```

### **Étape 3 : Build de Développement Android**
```bash
# Créer le build de développement
eas build --platform android --profile development

# Alternative : Build local (plus rapide)
eas build --platform android --profile development --local
```

### **Étape 4 : Installation sur Appareil**
```bash
# Une fois le build terminé, installer l'APK
# L'URL de téléchargement sera fournie par EAS

# Ou utiliser ADB si connecté
adb install app-debug.apk
```

## 📱 **LANCEMENT ET TEST**

### **Démarrer le Serveur de Développement**
```bash
# Démarrer Expo
expo start --dev-client

# Ou avec tunnel pour accès externe
expo start --dev-client --tunnel
```

### **Tester les Services B2B**
1. **Ouvrir l'application** sur votre appareil Android
2. **Se connecter** avec vos identifiants
3. **Naviguer vers les services B2B** :
   - Onglet "Business" dans la navigation principale
   - Ou via le menu hamburger

## 🧪 **TESTS DES FONCTIONNALITÉS**

### **1. Dashboard B2B**
- ✅ Vérifier l'affichage des métriques
- ✅ Tester les graphiques de revenus
- ✅ Valider les actions rapides

### **2. Gestion Partenaires**
- ✅ Ajouter un nouveau partenaire
- ✅ Modifier les informations
- ✅ Tester les filtres par statut

### **3. Commandes en Masse**
- ✅ Créer une commande bulk
- ✅ Suivre la progression
- ✅ Tester les actions (détails, rapport)

### **4. Micro-Entrepôts**
- ✅ Ajouter un entrepôt
- ✅ Gérer l'inventaire
- ✅ Vérifier les alertes stock

### **5. Services Premium**
- ✅ Créer une livraison premium
- ✅ Tester les différents types
- ✅ Valider la tarification

### **6. Services Financiers**
- ✅ Dashboard financier
- ✅ Collecte de paiements
- ✅ Métriques et analytics

## 🐛 **DÉPANNAGE**

### **Problème : Build Failed**
```bash
# Nettoyer et recommencer
expo r -c
rm -rf node_modules
npm install
eas build --platform android --profile development --clear-cache
```

### **Problème : App Crash au Démarrage**
```bash
# Vérifier les logs
expo logs --platform android

# Ou via ADB
adb logcat | grep -i expo
```

### **Problème : Services B2B Non Visibles**
1. Vérifier que l'utilisateur a le rôle approprié
2. Contrôler la configuration de navigation
3. Valider les permissions Supabase

### **Problème : Erreurs Supabase**
```bash
# Tester la connexion
node scripts/test-connection.js

# Vérifier les variables d'environnement
expo config --type public
```

## 📊 **MÉTRIQUES DE TEST**

### **Performance Attendue**
- **Temps de démarrage :** < 3 secondes
- **Navigation B2B :** < 1 seconde
- **Chargement données :** < 2 secondes
- **Actions CRUD :** < 1 seconde

### **Fonctionnalités Critiques**
- ✅ **Authentification** : Login/logout
- ✅ **Navigation B2B** : Accès aux 5 onglets
- ✅ **CRUD Partenaires** : Créer/modifier/supprimer
- ✅ **Analytics** : Graphiques et métriques
- ✅ **Offline** : Fonctionnement sans réseau

## 🚀 **DÉPLOIEMENT STAGING**

### **Build Staging**
```bash
# Build pour staging
eas build --platform android --profile staging

# Soumettre pour test interne
eas submit --platform android --profile staging
```

### **Variables Staging**
```env
EXPO_PUBLIC_ENV=staging
STAGING_SUPABASE_URL=votre_staging_url
STAGING_SUPABASE_ANON_KEY=votre_staging_key
STAGING_GOOGLE_MAPS_API_KEY=votre_staging_maps_key
```

## 📈 **VALIDATION BUSINESS**

### **Scénarios de Test Business**
1. **Partenaire Jumia** :
   - Créer partenaire "Jumia CI"
   - Configurer API Jumia
   - Tester sync commandes

2. **Services Premium** :
   - Livraison document (2,500 FCFA)
   - Livraison haute valeur (5,000+ FCFA)
   - Livraison express (3,500 FCFA)

3. **Micro-Entrepôt** :
   - Créer entrepôt "Abidjan Centre"
   - Ajouter inventaire produits
   - Tester alertes stock

4. **Services Financiers** :
   - Collecte paiement Orange Money
   - Assurance livraison
   - Micro-crédit livreur

## 🎯 **CRITÈRES DE SUCCÈS**

### **Technique**
- ✅ Build réussi sans erreurs
- ✅ App démarre correctement
- ✅ Navigation fluide
- ✅ Pas de crash pendant 30 min d'utilisation

### **Fonctionnel**
- ✅ Tous les écrans B2B accessibles
- ✅ CRUD partenaires fonctionnel
- ✅ Analytics affichent des données
- ✅ Services premium configurables

### **Business**
- ✅ Calculs de revenus corrects
- ✅ Commissions appliquées
- ✅ Métriques cohérentes
- ✅ Workflows business complets

## 📞 **SUPPORT**

### **En cas de problème :**
1. **Vérifier les logs** : `expo logs`
2. **Nettoyer le cache** : `expo r -c`
3. **Réinstaller dépendances** : `rm -rf node_modules && npm install`
4. **Rebuild complet** : `eas build --clear-cache`

### **Ressources Utiles**
- [Documentation EAS Build](https://docs.expo.dev/build/introduction/)
- [Expo Development Build](https://docs.expo.dev/development/build/)
- [Troubleshooting Guide](https://docs.expo.dev/troubleshooting/build-errors/)

---

## 🎉 **RÉSULTAT ATTENDU**

Après avoir suivi ce guide, vous devriez avoir :
- ✅ **Application fonctionnelle** avec services B2B
- ✅ **Tous les écrans** accessibles et opérationnels
- ✅ **Métriques de revenus** calculées correctement
- ✅ **Système prêt** pour génération de revenus

**L'application Mientior Livraison est maintenant transformée en plateforme logistique complète pour l'Afrique !** 🚀🌍💰
