import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

import { colors, typography, spacing } from '../../constants/theme';
import { DeliveryService } from '../../types/enhancedServices';
import { CustomTextInput } from '../../components/CustomTextInput';
import { CustomButton } from '../../components/CustomButton';
import { useAuth } from '../../hooks/useAuth';

interface ServiceRequestForm {
  businessName?: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  serviceDetails: string;
  estimatedVolume: string;
  preferredStartDate: string;
  additionalRequirements: string;
}

const ServiceRequestScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  
  // @ts-ignore
  const service: DeliveryService = route.params?.service;

  const [formData, setFormData] = useState<ServiceRequestForm>({
    contactPerson: user?.full_name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    address: '',
    serviceDetails: '',
    estimatedVolume: '',
    preferredStartDate: '',
    additionalRequirements: '',
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<ServiceRequestForm>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<ServiceRequestForm> = {};

    if (!formData.contactPerson.trim()) {
      newErrors.contactPerson = 'Nom du contact requis';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email requis';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format email invalide';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Téléphone requis';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Adresse requise';
    }

    if (!formData.serviceDetails.trim()) {
      newErrors.serviceDetails = 'Détails du service requis';
    }

    if (service.targetMarket === 'b2b' && !formData.businessName?.trim()) {
      newErrors.businessName = 'Nom de l\'entreprise requis pour les services B2B';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      // Simuler l'envoi de la demande
      await new Promise(resolve => setTimeout(resolve, 2000));

      Alert.alert(
        'Demande envoyée',
        'Votre demande de service a été envoyée avec succès. Notre équipe vous contactera dans les 24h.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', 'Impossible d\'envoyer la demande. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: keyof ServiceRequestForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (!service) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Service non trouvé</Text>
          <CustomButton title="Retour" onPress={() => navigation.goBack()} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Demande de Service</Text>
          <Text style={styles.headerSubtitle}>{service.name}</Text>
        </View>
      </View>

      <KeyboardAvoidingView 
        style={styles.flex} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Service Info */}
          <View style={styles.serviceInfoCard}>
            <View style={styles.serviceHeader}>
              <View style={styles.serviceIcon}>
                <Ionicons name={service.icon as any} size={24} color={colors.primary[500]} />
              </View>
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceName}>{service.name}</Text>
                <Text style={styles.serviceDescription}>{service.description}</Text>
              </View>
            </View>
            
            <View style={styles.servicePricing}>
              <Text style={styles.priceLabel}>Prix de base</Text>
              <Text style={styles.priceAmount}>
                {service.pricing.basePrice.toLocaleString()} {service.pricing.currency}
              </Text>
              <Text style={styles.pricingNote}>
                {service.pricing.pricingModel === 'fixed' ? 'Prix fixe' :
                 service.pricing.pricingModel === 'distance' ? 'Prix par kilomètre' :
                 service.pricing.pricingModel === 'weight' ? 'Prix par kilogramme' :
                 service.pricing.pricingModel === 'time' ? 'Prix par heure' :
                 'Prix basé sur la valeur'}
              </Text>
            </View>
          </View>

          {/* Form */}
          <View style={styles.form}>
            <Text style={styles.formTitle}>Informations de contact</Text>

            {service.targetMarket === 'b2b' && (
              <CustomTextInput
                label="Nom de l'entreprise"
                value={formData.businessName || ''}
                onChangeText={(text) => updateFormData('businessName', text)}
                error={errors.businessName}
                placeholder="Nom de votre entreprise"
                required
              />
            )}

            <CustomTextInput
              label="Nom du contact"
              value={formData.contactPerson}
              onChangeText={(text) => updateFormData('contactPerson', text)}
              error={errors.contactPerson}
              placeholder="Votre nom complet"
              required
            />

            <CustomTextInput
              label="Email"
              value={formData.email}
              onChangeText={(text) => updateFormData('email', text)}
              error={errors.email}
              placeholder="<EMAIL>"
              keyboardType="email-address"
              autoCapitalize="none"
              required
            />

            <CustomTextInput
              label="Téléphone"
              value={formData.phone}
              onChangeText={(text) => updateFormData('phone', text)}
              error={errors.phone}
              placeholder="+225 XX XX XX XX XX"
              keyboardType="phone-pad"
              required
            />

            <CustomTextInput
              label="Adresse"
              value={formData.address}
              onChangeText={(text) => updateFormData('address', text)}
              error={errors.address}
              placeholder="Adresse complète"
              multiline
              numberOfLines={2}
              required
            />

            <Text style={styles.formTitle}>Détails du service</Text>

            <CustomTextInput
              label="Description de vos besoins"
              value={formData.serviceDetails}
              onChangeText={(text) => updateFormData('serviceDetails', text)}
              error={errors.serviceDetails}
              placeholder="Décrivez précisément vos besoins..."
              multiline
              numberOfLines={4}
              required
            />

            <CustomTextInput
              label="Volume estimé"
              value={formData.estimatedVolume}
              onChangeText={(text) => updateFormData('estimatedVolume', text)}
              error={errors.estimatedVolume}
              placeholder="Ex: 50 colis/mois, 10kg/jour..."
            />

            <CustomTextInput
              label="Date de début souhaitée"
              value={formData.preferredStartDate}
              onChangeText={(text) => updateFormData('preferredStartDate', text)}
              error={errors.preferredStartDate}
              placeholder="Ex: Dans 2 semaines, 1er janvier..."
            />

            <CustomTextInput
              label="Exigences particulières"
              value={formData.additionalRequirements}
              onChangeText={(text) => updateFormData('additionalRequirements', text)}
              error={errors.additionalRequirements}
              placeholder="Horaires spéciaux, équipements particuliers..."
              multiline
              numberOfLines={3}
            />
          </View>

          {/* Service Requirements */}
          {service.requirements.length > 0 && (
            <View style={styles.requirementsCard}>
              <Text style={styles.requirementsTitle}>Prérequis pour ce service</Text>
              {service.requirements.map((requirement, index) => (
                <View key={index} style={styles.requirementItem}>
                  <Ionicons name="checkmark-circle" size={16} color={colors.warning} />
                  <Text style={styles.requirementText}>{requirement}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Submit Button */}
          <View style={styles.submitContainer}>
            <CustomButton
              title="Envoyer la demande"
              onPress={handleSubmit}
              loading={loading}
              disabled={loading}
              gradient
              leftIcon="send"
            />
            
            <Text style={styles.submitNote}>
              Notre équipe vous contactera dans les 24h pour finaliser votre demande
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  flex: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
  },
  serviceInfoCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
    padding: spacing.lg,
    marginVertical: spacing.md,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  serviceIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 20,
  },
  servicePricing: {
    alignItems: 'center',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.neutral[200],
  },
  priceLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  priceAmount: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
    marginBottom: 4,
  },
  pricingNote: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
    fontStyle: 'italic',
  },
  form: {
    gap: spacing.md,
  },
  formTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  requirementsCard: {
    backgroundColor: colors.warning + '10',
    borderRadius: 12,
    padding: spacing.md,
    marginTop: spacing.lg,
    borderWidth: 1,
    borderColor: colors.warning + '30',
  },
  requirementsTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.xs,
  },
  requirementText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    flex: 1,
  },
  submitContainer: {
    marginTop: spacing.xl,
    alignItems: 'center',
  },
  submitNote: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    textAlign: 'center',
    marginTop: spacing.md,
    lineHeight: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  errorText: {
    fontSize: typography.fontSize.lg,
    color: colors.error,
    marginBottom: spacing.lg,
  },
});

export default ServiceRequestScreen;
