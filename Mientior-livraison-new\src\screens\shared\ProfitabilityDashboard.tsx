import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

import { colors, typography, spacing } from '../../constants/theme';
import { financialServices, CashManagementMetrics } from '../../services/financialServices';
import { ENHANCED_SERVICE_CATEGORIES } from '../../types/enhancedServices';

const { width } = Dimensions.get('window');

interface ProfitabilityMetrics {
  totalRevenue: number;
  totalCosts: number;
  netProfit: number;
  profitMargin: number;
  serviceBreakdown: {
    serviceId: string;
    serviceName: string;
    revenue: number;
    margin: number;
    volume: number;
  }[];
  financialServices: CashManagementMetrics;
}

const ProfitabilityDashboard: React.FC = () => {
  const navigation = useNavigation();
  const [metrics, setMetrics] = useState<ProfitabilityMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month'>('month');

  useEffect(() => {
    loadMetrics();
  }, [selectedPeriod]);

  const loadMetrics = async () => {
    try {
      setLoading(true);
      
      // Simuler le chargement des métriques
      const [cashMetrics] = await Promise.all([
        financialServices.getCashManagementMetrics(),
        // Ajouter d'autres métriques ici
      ]);

      // Simuler des données de rentabilité
      const mockMetrics: ProfitabilityMetrics = {
        totalRevenue: 2850000,
        totalCosts: 1920000,
        netProfit: 930000,
        profitMargin: 32.6,
        serviceBreakdown: [
          {
            serviceId: 'ecommerce_lastmile',
            serviceName: 'Livraison E-commerce',
            revenue: 1200000,
            margin: 35,
            volume: 450,
          },
          {
            serviceId: 'pharmacy_express',
            serviceName: 'Pharmacie Express',
            revenue: 680000,
            margin: 28,
            volume: 320,
          },
          {
            serviceId: 'micro_warehouse',
            serviceName: 'Micro-Entrepôts',
            revenue: 540000,
            margin: 40,
            volume: 25,
          },
          {
            serviceId: 'shared_logistics_platform',
            serviceName: 'Plateforme Logistique',
            revenue: 430000,
            margin: 45,
            volume: 8,
          },
        ],
        financialServices: cashMetrics,
      };

      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Erreur chargement métriques:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadMetrics();
  };

  const renderMetricCard = (
    title: string,
    value: string,
    subtitle: string,
    icon: string,
    color: string,
    trend?: { value: number; isPositive: boolean }
  ) => (
    <View style={[styles.metricCard, { borderLeftColor: color }]}>
      <View style={styles.metricHeader}>
        <View style={[styles.metricIcon, { backgroundColor: color + '20' }]}>
          <Ionicons name={icon as any} size={20} color={color} />
        </View>
        {trend && (
          <View style={styles.trendContainer}>
            <Ionicons 
              name={trend.isPositive ? 'trending-up' : 'trending-down'} 
              size={16} 
              color={trend.isPositive ? colors.success : colors.error} 
            />
            <Text style={[
              styles.trendText,
              { color: trend.isPositive ? colors.success : colors.error }
            ]}>
              {Math.abs(trend.value)}%
            </Text>
          </View>
        )}
      </View>
      <Text style={styles.metricValue}>{value}</Text>
      <Text style={styles.metricTitle}>{title}</Text>
      <Text style={styles.metricSubtitle}>{subtitle}</Text>
    </View>
  );

  const renderServiceBreakdown = () => {
    if (!metrics) return null;

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Rentabilité par Service</Text>
        {metrics.serviceBreakdown.map((service, index) => (
          <View key={service.serviceId} style={styles.serviceItem}>
            <View style={styles.serviceInfo}>
              <Text style={styles.serviceName}>{service.serviceName}</Text>
              <Text style={styles.serviceVolume}>{service.volume} commandes</Text>
            </View>
            <View style={styles.serviceMetrics}>
              <Text style={styles.serviceRevenue}>
                {service.revenue.toLocaleString()} XOF
              </Text>
              <View style={[styles.marginBadge, { backgroundColor: getMarginColor(service.margin) + '20' }]}>
                <Text style={[styles.marginText, { color: getMarginColor(service.margin) }]}>
                  {service.margin}%
                </Text>
              </View>
            </View>
          </View>
        ))}
      </View>
    );
  };

  const getMarginColor = (margin: number): string => {
    if (margin >= 35) return colors.success;
    if (margin >= 25) return colors.warning;
    return colors.error;
  };

  const renderFinancialServices = () => {
    if (!metrics) return null;

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Services Financiers</Text>
        <View style={styles.financialGrid}>
          <View style={styles.financialItem}>
            <Text style={styles.financialLabel}>Cash en Transit</Text>
            <Text style={styles.financialValue}>
              {metrics.financialServices.totalCashInTransit.toLocaleString()} XOF
            </Text>
          </View>
          <View style={styles.financialItem}>
            <Text style={styles.financialLabel}>Collectes Journalières</Text>
            <Text style={styles.financialValue}>
              {metrics.financialServices.dailyCollections.toLocaleString()} XOF
            </Text>
          </View>
          <View style={styles.financialItem}>
            <Text style={styles.financialLabel}>Couverture Assurance</Text>
            <Text style={styles.financialValue}>
              {metrics.financialServices.insuranceCoverage.toLocaleString()} XOF
            </Text>
          </View>
          <View style={styles.financialItem}>
            <Text style={styles.financialLabel}>Profit Services Financiers</Text>
            <Text style={[styles.financialValue, { color: colors.success }]}>
              {metrics.financialServices.profitFromFinancialServices.toLocaleString()} XOF
            </Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading && !metrics) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Chargement des métriques...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Tableau de Rentabilité</Text>
          <Text style={styles.headerSubtitle}>Analyse des performances</Text>
        </View>
      </View>

      {/* Period Selector */}
      <View style={styles.periodSelector}>
        {(['day', 'week', 'month'] as const).map((period) => (
          <TouchableOpacity
            key={period}
            style={[
              styles.periodButton,
              selectedPeriod === period && styles.periodButtonActive,
            ]}
            onPress={() => setSelectedPeriod(period)}
          >
            <Text style={[
              styles.periodButtonText,
              selectedPeriod === period && styles.periodButtonTextActive,
            ]}>
              {period === 'day' ? 'Jour' : period === 'week' ? 'Semaine' : 'Mois'}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {metrics && (
          <>
            {/* Main Metrics */}
            <View style={styles.metricsGrid}>
              {renderMetricCard(
                'Chiffre d\'Affaires',
                `${metrics.totalRevenue.toLocaleString()} XOF`,
                'Total des revenus',
                'trending-up',
                colors.primary[500],
                { value: 12.5, isPositive: true }
              )}
              
              {renderMetricCard(
                'Coûts Opérationnels',
                `${metrics.totalCosts.toLocaleString()} XOF`,
                'Total des coûts',
                'trending-down',
                colors.warning,
                { value: 8.2, isPositive: false }
              )}
              
              {renderMetricCard(
                'Profit Net',
                `${metrics.netProfit.toLocaleString()} XOF`,
                'Bénéfice après coûts',
                'cash',
                colors.success,
                { value: 18.7, isPositive: true }
              )}
              
              {renderMetricCard(
                'Marge Bénéficiaire',
                `${metrics.profitMargin.toFixed(1)}%`,
                'Rentabilité globale',
                'pie-chart',
                colors.secondary[500],
                { value: 5.3, isPositive: true }
              )}
            </View>

            {/* Service Breakdown */}
            {renderServiceBreakdown()}

            {/* Financial Services */}
            {renderFinancialServices()}

            {/* Recommendations */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Recommandations</Text>
              <View style={styles.recommendationCard}>
                <Ionicons name="bulb" size={24} color={colors.warning} />
                <View style={styles.recommendationContent}>
                  <Text style={styles.recommendationTitle}>Optimisation Suggérée</Text>
                  <Text style={styles.recommendationText}>
                    Augmentez la part des services B2B (marge 35%+) et développez les services financiers 
                    pour améliorer la rentabilité globale.
                  </Text>
                </View>
              </View>
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    gap: spacing.sm,
  },
  periodButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 8,
    backgroundColor: colors.neutral[100],
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: colors.primary[500],
  },
  periodButtonText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.secondary,
  },
  periodButtonTextActive: {
    color: colors.text.inverse,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
    marginVertical: spacing.md,
  },
  metricCard: {
    flex: 1,
    minWidth: (width - spacing.md * 3) / 2,
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    borderLeftWidth: 4,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  metricIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  trendText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  metricValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: 2,
  },
  metricTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: 2,
  },
  metricSubtitle: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
  },
  section: {
    marginVertical: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  serviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.sm,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: 2,
  },
  serviceVolume: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  serviceMetrics: {
    alignItems: 'flex-end',
  },
  serviceRevenue: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: 4,
  },
  marginBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 8,
  },
  marginText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  financialGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  financialItem: {
    flex: 1,
    minWidth: (width - spacing.md * 3) / 2,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    padding: spacing.md,
    alignItems: 'center',
  },
  financialLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  financialValue: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    textAlign: 'center',
  },
  recommendationCard: {
    flexDirection: 'row',
    backgroundColor: colors.warning + '10',
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.warning + '30',
  },
  recommendationContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  recommendationTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  recommendationText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 20,
  },
});

export default ProfitabilityDashboard;
