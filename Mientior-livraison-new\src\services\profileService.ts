import { supabase, utilityService } from './supabase';
import { User } from '../types';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';

export interface ProfileUpdateData {
  full_name?: string;
  phone?: string;
  email?: string;
  avatar_url?: string;
  preferred_language?: string;
  notification_preferences?: {
    push_enabled: boolean;
    email_enabled: boolean;
    sms_enabled: boolean;
    order_updates: boolean;
    promotional: boolean;
  };
}

export interface ImagePickerOptions {
  allowsEditing?: boolean;
  aspect?: [number, number];
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
}

class ProfileService {
  
  /**
   * Demander les permissions pour accéder à la galerie et à la caméra
   */
  async requestImagePermissions(): Promise<boolean> {
    try {
      // Demander permission pour la galerie
      const { status: galleryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      // Demander permission pour la caméra
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (galleryStatus !== 'granted' || cameraStatus !== 'granted') {
        console.warn('⚠️ Permissions caméra/galerie non accordées');
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('❌ Erreur demande permissions:', error);
      return false;
    }
  }

  /**
   * Sélectionner une image depuis la galerie
   */
  async selectImageFromGallery(options: ImagePickerOptions = {}): Promise<string | null> {
    try {
      const hasPermission = await this.requestImagePermissions();
      if (!hasPermission) {
        throw new Error('Permissions requises pour accéder à la galerie');
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: options.allowsEditing ?? true,
        aspect: options.aspect ?? [1, 1],
        quality: options.quality ?? 0.8,
        allowsMultipleSelection: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return null;
      }

      return result.assets[0].uri;
    } catch (error) {
      console.error('❌ Erreur sélection image:', error);
      throw error;
    }
  }

  /**
   * Prendre une photo avec la caméra
   */
  async takePhotoWithCamera(options: ImagePickerOptions = {}): Promise<string | null> {
    try {
      const hasPermission = await this.requestImagePermissions();
      if (!hasPermission) {
        throw new Error('Permissions requises pour accéder à la caméra');
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: options.allowsEditing ?? true,
        aspect: options.aspect ?? [1, 1],
        quality: options.quality ?? 0.8,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return null;
      }

      return result.assets[0].uri;
    } catch (error) {
      console.error('❌ Erreur prise photo:', error);
      throw error;
    }
  }

  /**
   * Compresser et redimensionner une image
   */
  async compressImage(uri: string, options: ImagePickerOptions = {}): Promise<string> {
    try {
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        uri,
        [
          {
            resize: {
              width: options.maxWidth ?? 400,
              height: options.maxHeight ?? 400,
            },
          },
        ],
        {
          compress: options.quality ?? 0.8,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      return manipulatedImage.uri;
    } catch (error) {
      console.error('❌ Erreur compression image:', error);
      throw error;
    }
  }

  /**
   * Uploader un avatar utilisateur
   */
  async uploadAvatar(imageUri: string, userId: string): Promise<string> {
    try {
      console.log('📸 Début upload avatar...');
      
      // Compresser l'image avant upload
      const compressedUri = await this.compressImage(imageUri, {
        maxWidth: 400,
        maxHeight: 400,
        quality: 0.8,
      });

      // Upload vers Supabase
      const avatarUrl = await utilityService.uploadUserAvatar(compressedUri, userId);
      
      console.log('✅ Avatar uploadé avec succès');
      return avatarUrl;
    } catch (error) {
      console.error('❌ Erreur upload avatar:', error);
      throw error;
    }
  }

  /**
   * Mettre à jour le profil utilisateur
   */
  async updateUserProfile(userId: string, updateData: ProfileUpdateData): Promise<User> {
    try {
      console.log('👤 Mise à jour profil utilisateur:', userId);
      
      // Mettre à jour la table users
      const { data: updatedUser, error: userError } = await supabase
        .from('users')
        .update({
          ...updateData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId)
        .select()
        .single();

      if (userError) {
        console.error('❌ Erreur mise à jour users:', userError);
        throw userError;
      }

      // Mettre à jour le profil spécifique selon le rôle
      if (updatedUser.role === 'client') {
        await this.updateClientProfile(userId, updateData);
      } else if (updatedUser.role === 'livreur') {
        await this.updateDeliveryProfile(userId, updateData);
      } else if (updatedUser.role === 'marchand') {
        await this.updateMerchantProfile(userId, updateData);
      }

      console.log('✅ Profil mis à jour avec succès');
      return updatedUser;
    } catch (error) {
      console.error('❌ Erreur mise à jour profil:', error);
      throw error;
    }
  }

  /**
   * Mettre à jour le profil client
   */
  private async updateClientProfile(userId: string, updateData: ProfileUpdateData): Promise<void> {
    try {
      const profileData: any = {};
      
      if (updateData.full_name) {
        const nameParts = updateData.full_name.split(' ');
        profileData.first_name = nameParts[0] || '';
        profileData.last_name = nameParts.slice(1).join(' ') || '';
      }

      if (Object.keys(profileData).length > 0) {
        profileData.updated_at = new Date().toISOString();
        
        const { error } = await supabase
          .from('client_profiles')
          .update(profileData)
          .eq('user_id', userId);

        if (error) {
          console.warn('⚠️ Erreur mise à jour client_profiles:', error);
        }
      }
    } catch (error) {
      console.warn('⚠️ Erreur mise à jour profil client:', error);
    }
  }

  /**
   * Mettre à jour le profil livreur
   */
  private async updateDeliveryProfile(userId: string, updateData: ProfileUpdateData): Promise<void> {
    try {
      const profileData: any = {};
      
      if (updateData.full_name) {
        profileData.full_name = updateData.full_name;
      }
      if (updateData.phone) {
        profileData.phone = updateData.phone;
      }

      if (Object.keys(profileData).length > 0) {
        profileData.updated_at = new Date().toISOString();
        
        const { error } = await supabase
          .from('delivery_person_profiles')
          .update(profileData)
          .eq('user_id', userId);

        if (error) {
          console.warn('⚠️ Erreur mise à jour delivery_person_profiles:', error);
        }
      }
    } catch (error) {
      console.warn('⚠️ Erreur mise à jour profil livreur:', error);
    }
  }

  /**
   * Mettre à jour le profil marchand
   */
  private async updateMerchantProfile(userId: string, updateData: ProfileUpdateData): Promise<void> {
    try {
      const profileData: any = {};
      
      if (updateData.full_name) {
        profileData.nom = updateData.full_name;
      }
      if (updateData.phone) {
        profileData.telephone = updateData.phone;
      }
      if (updateData.email) {
        profileData.email = updateData.email;
      }

      if (Object.keys(profileData).length > 0) {
        profileData.updated_at = new Date().toISOString();
        
        const { error } = await supabase
          .from('merchant_profiles')
          .update(profileData)
          .eq('user_id', userId);

        if (error) {
          console.warn('⚠️ Erreur mise à jour merchant_profiles:', error);
        }
      }
    } catch (error) {
      console.warn('⚠️ Erreur mise à jour profil marchand:', error);
    }
  }

  /**
   * Obtenir le profil utilisateur complet
   */
  async getUserProfile(userId: string): Promise<User | null> {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('❌ Erreur récupération profil:', error);
        return null;
      }

      return user;
    } catch (error) {
      console.error('❌ Erreur récupération profil:', error);
      return null;
    }
  }

  /**
   * Supprimer l'avatar utilisateur
   */
  async deleteAvatar(userId: string, currentAvatarUrl?: string): Promise<void> {
    try {
      // Supprimer l'ancien fichier si il existe
      if (currentAvatarUrl) {
        await utilityService.deleteUserAvatar(currentAvatarUrl);
      }

      // Mettre à jour le profil pour supprimer l'URL
      await this.updateUserProfile(userId, { avatar_url: null });
      
      console.log('✅ Avatar supprimé avec succès');
    } catch (error) {
      console.error('❌ Erreur suppression avatar:', error);
      throw error;
    }
  }
}

export const profileService = new ProfileService();
export default profileService;
