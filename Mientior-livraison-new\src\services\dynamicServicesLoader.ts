// Service de chargement dynamique des services spécialisés
import { supabase } from './supabase';
import { SpecializedServiceCategory, SpecializedService } from '../types/specializedServices';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ServiceCache {
  categories: SpecializedServiceCategory[];
  lastUpdated: string;
  version: string;
}

interface ServiceUpdateNotification {
  type: 'category_added' | 'category_updated' | 'category_removed' | 'service_added' | 'service_updated' | 'service_removed';
  categoryId?: string;
  serviceId?: string;
  data?: any;
  timestamp: string;
}

class DynamicServicesLoader {
  private cache: ServiceCache | null = null;
  private cacheKey = 'specialized_services_cache';
  private cacheExpiry = 30 * 60 * 1000; // 30 minutes
  private listeners: ((categories: SpecializedServiceCategory[]) => void)[] = [];

  /**
   * Charger les services depuis le cache ou l'API
   */
  async loadServices(forceRefresh: boolean = false): Promise<SpecializedServiceCategory[]> {
    try {
      console.log('🔄 Chargement des services spécialisés...');

      // Vérifier le cache d'abord
      if (!forceRefresh && this.cache) {
        const cacheAge = Date.now() - new Date(this.cache.lastUpdated).getTime();
        if (cacheAge < this.cacheExpiry) {
          console.log('✅ Services chargés depuis le cache');
          return this.cache.categories;
        }
      }

      // Charger depuis l'API
      const categories = await this.fetchServicesFromAPI();
      
      // Mettre à jour le cache
      await this.updateCache(categories);
      
      // Notifier les listeners
      this.notifyListeners(categories);

      return categories;
    } catch (error) {
      console.error('❌ Erreur chargement services:', error);
      
      // Fallback vers le cache si disponible
      if (this.cache) {
        console.log('⚠️ Utilisation du cache en fallback');
        return this.cache.categories;
      }
      
      throw error;
    }
  }

  /**
   * Récupérer les services depuis l'API
   */
  private async fetchServicesFromAPI(): Promise<SpecializedServiceCategory[]> {
    try {
      // Récupérer les catégories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('specialized_service_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      if (categoriesError) throw categoriesError;

      // Récupérer les services pour chaque catégorie
      const categories: SpecializedServiceCategory[] = [];

      for (const categoryData of categoriesData || []) {
        const { data: servicesData, error: servicesError } = await supabase
          .from('specialized_services')
          .select('*')
          .eq('category_id', categoryData.id)
          .eq('is_active', true)
          .order('display_order');

        if (servicesError) throw servicesError;

        const category: SpecializedServiceCategory = {
          id: categoryData.id,
          name: categoryData.name,
          description: categoryData.description,
          icon: categoryData.icon,
          color: categoryData.color,
          profitability: categoryData.profitability,
          estimatedMargin: categoryData.estimated_margin,
          specialRequirements: categoryData.special_requirements || [],
          partnerRequirements: categoryData.partner_requirements || [],
          services: (servicesData || []).map(this.mapServiceFromDB),
        };

        categories.push(category);
      }

      console.log(`✅ ${categories.length} catégories chargées depuis l'API`);
      return categories;
    } catch (error) {
      console.error('❌ Erreur récupération API:', error);
      throw error;
    }
  }

  /**
   * Mapper un service depuis la base de données
   */
  private mapServiceFromDB(serviceData: any): SpecializedService {
    return {
      id: serviceData.id,
      name: serviceData.name,
      description: serviceData.description,
      icon: serviceData.icon,
      category: serviceData.category_id,
      pricing: {
        basePrice: serviceData.base_price,
        currency: serviceData.currency,
        pricingModel: serviceData.pricing_model,
        weightTiers: serviceData.weight_tiers,
        sizeTiers: serviceData.size_tiers,
        distanceTiers: serviceData.distance_tiers,
        subscriptionPlans: serviceData.subscription_plans,
        additionalFees: serviceData.additional_fees || {},
        discounts: serviceData.discounts,
      },
      features: serviceData.features || [],
      requirements: serviceData.requirements || [],
      availability: {
        cities: serviceData.available_cities || [],
        timeSlots: serviceData.time_slots || [],
        maxDeliveryTime: serviceData.max_delivery_time,
        minDeliveryTime: serviceData.min_delivery_time,
        emergencyAvailable: serviceData.emergency_available || false,
        subscriptionAvailable: serviceData.subscription_available || false,
      },
      profitMetrics: {
        averageOrderValue: serviceData.average_order_value,
        operationalCost: serviceData.operational_cost,
        netMargin: serviceData.net_margin,
        volumeMultiplier: serviceData.volume_multiplier,
        riskLevel: serviceData.risk_level,
        seasonalVariation: serviceData.seasonal_variation,
      },
      specialHandling: {
        fragileItems: serviceData.fragile_items || false,
        temperatureControl: serviceData.temperature_control || false,
        secureTransport: serviceData.secure_transport || false,
        specialPackaging: serviceData.special_packaging || false,
        signatureRequired: serviceData.signature_required || false,
        photoProof: serviceData.photo_proof || false,
        realTimeTracking: serviceData.real_time_tracking || false,
        insuranceCoverage: serviceData.insurance_coverage || false,
        customInstructions: serviceData.custom_instructions || false,
      },
      partnerIntegration: {
        partnerType: serviceData.partner_type,
        verificationRequired: serviceData.verification_required || false,
        inventoryIntegration: serviceData.inventory_integration || false,
        appointmentBooking: serviceData.appointment_booking || false,
        customWorkflow: serviceData.custom_workflow || false,
        qualityStandards: serviceData.quality_standards || [],
        certificationRequired: serviceData.certification_required || [],
      },
    };
  }

  /**
   * Mettre à jour le cache
   */
  private async updateCache(categories: SpecializedServiceCategory[]): Promise<void> {
    try {
      const cacheData: ServiceCache = {
        categories,
        lastUpdated: new Date().toISOString(),
        version: '1.0.0',
      };

      this.cache = cacheData;
      await AsyncStorage.setItem(this.cacheKey, JSON.stringify(cacheData));
      
      console.log('✅ Cache mis à jour');
    } catch (error) {
      console.error('❌ Erreur mise à jour cache:', error);
    }
  }

  /**
   * Charger le cache depuis le stockage local
   */
  async loadFromCache(): Promise<SpecializedServiceCategory[] | null> {
    try {
      const cachedData = await AsyncStorage.getItem(this.cacheKey);
      if (!cachedData) return null;

      const cache: ServiceCache = JSON.parse(cachedData);
      const cacheAge = Date.now() - new Date(cache.lastUpdated).getTime();

      if (cacheAge < this.cacheExpiry) {
        this.cache = cache;
        console.log('✅ Cache chargé depuis le stockage local');
        return cache.categories;
      } else {
        console.log('⚠️ Cache expiré');
        await AsyncStorage.removeItem(this.cacheKey);
        return null;
      }
    } catch (error) {
      console.error('❌ Erreur chargement cache:', error);
      return null;
    }
  }

  /**
   * Obtenir un service spécifique par ID
   */
  async getServiceById(serviceId: string): Promise<SpecializedService | null> {
    const categories = await this.loadServices();
    
    for (const category of categories) {
      const service = category.services.find(s => s.id === serviceId);
      if (service) return service;
    }
    
    return null;
  }

  /**
   * Obtenir une catégorie spécifique par ID
   */
  async getCategoryById(categoryId: string): Promise<SpecializedServiceCategory | null> {
    const categories = await this.loadServices();
    return categories.find(c => c.id === categoryId) || null;
  }

  /**
   * Rechercher des services par critères
   */
  async searchServices(criteria: {
    query?: string;
    categoryId?: string;
    city?: string;
    priceRange?: { min: number; max: number };
    features?: string[];
  }): Promise<SpecializedService[]> {
    const categories = await this.loadServices();
    let services: SpecializedService[] = [];

    // Collecter tous les services
    categories.forEach(category => {
      if (!criteria.categoryId || category.id === criteria.categoryId) {
        services.push(...category.services);
      }
    });

    // Filtrer par critères
    if (criteria.query) {
      const query = criteria.query.toLowerCase();
      services = services.filter(service => 
        service.name.toLowerCase().includes(query) ||
        service.description.toLowerCase().includes(query)
      );
    }

    if (criteria.city) {
      services = services.filter(service => 
        service.availability.cities.includes(criteria.city!)
      );
    }

    if (criteria.priceRange) {
      services = services.filter(service => 
        service.pricing.basePrice >= criteria.priceRange!.min &&
        service.pricing.basePrice <= criteria.priceRange!.max
      );
    }

    if (criteria.features && criteria.features.length > 0) {
      services = services.filter(service => 
        criteria.features!.some(feature => 
          service.features.some(f => f.toLowerCase().includes(feature.toLowerCase()))
        )
      );
    }

    return services;
  }

  /**
   * Écouter les mises à jour de services
   */
  addUpdateListener(callback: (categories: SpecializedServiceCategory[]) => void): void {
    this.listeners.push(callback);
  }

  /**
   * Supprimer un listener
   */
  removeUpdateListener(callback: (categories: SpecializedServiceCategory[]) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  /**
   * Notifier tous les listeners
   */
  private notifyListeners(categories: SpecializedServiceCategory[]): void {
    this.listeners.forEach(listener => {
      try {
        listener(categories);
      } catch (error) {
        console.error('❌ Erreur notification listener:', error);
      }
    });
  }

  /**
   * Forcer le rechargement des services
   */
  async refreshServices(): Promise<SpecializedServiceCategory[]> {
    return this.loadServices(true);
  }

  /**
   * Vider le cache
   */
  async clearCache(): Promise<void> {
    try {
      this.cache = null;
      await AsyncStorage.removeItem(this.cacheKey);
      console.log('✅ Cache vidé');
    } catch (error) {
      console.error('❌ Erreur vidage cache:', error);
    }
  }

  /**
   * Obtenir les statistiques du cache
   */
  getCacheStats(): { 
    isCached: boolean; 
    lastUpdated?: string; 
    categoriesCount?: number; 
    servicesCount?: number; 
  } {
    if (!this.cache) {
      return { isCached: false };
    }

    const servicesCount = this.cache.categories.reduce(
      (total, category) => total + category.services.length, 
      0
    );

    return {
      isCached: true,
      lastUpdated: this.cache.lastUpdated,
      categoriesCount: this.cache.categories.length,
      servicesCount,
    };
  }
}

export const dynamicServicesLoader = new DynamicServicesLoader();
export default dynamicServicesLoader;
