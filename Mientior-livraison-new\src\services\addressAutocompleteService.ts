// Service d'autocomplétion d'adresses avec Google Places API
import { Platform } from 'react-native';

export interface AddressSuggestion {
  id: string;
  description: string;
  mainText: string;
  secondaryText: string;
  placeId: string;
  types: string[];
}

export interface AddressDetails {
  placeId: string;
  formattedAddress: string;
  streetNumber?: string;
  streetName?: string;
  locality?: string; // Ville
  sublocality?: string; // Quartier
  administrativeAreaLevel1?: string; // Région/État
  administrativeAreaLevel2?: string; // Département
  country?: string;
  postalCode?: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  addressComponents: AddressComponent[];
}

export interface AddressComponent {
  longName: string;
  shortName: string;
  types: string[];
}

export interface AddressValidationResult {
  isValid: boolean;
  confidence: 'high' | 'medium' | 'low';
  suggestions?: AddressSuggestion[];
  correctedAddress?: AddressDetails;
  issues?: string[];
}

class AddressAutocompleteService {
  private apiKey: string;
  private baseUrl = 'https://maps.googleapis.com/maps/api/place';
  private sessionToken: string | null = null;

  constructor() {
    // En production, cette clé devrait venir des variables d'environnement
    this.apiKey = 'YOUR_GOOGLE_PLACES_API_KEY'; // À remplacer par votre vraie clé
  }

  /**
   * Générer un token de session pour optimiser les coûts
   */
  private generateSessionToken(): string {
    if (!this.sessionToken) {
      this.sessionToken = Math.random().toString(36).substring(2, 15) + 
                         Math.random().toString(36).substring(2, 15);
    }
    return this.sessionToken;
  }

  /**
   * Réinitialiser le token de session
   */
  private resetSessionToken(): void {
    this.sessionToken = null;
  }

  /**
   * Obtenir des suggestions d'adresses
   */
  async getAddressSuggestions(
    input: string,
    options: {
      country?: string;
      region?: string;
      types?: string[];
      radius?: number;
      location?: { latitude: number; longitude: number };
    } = {}
  ): Promise<AddressSuggestion[]> {
    try {
      if (input.length < 3) {
        return [];
      }

      console.log('🔍 Recherche suggestions adresse:', input);

      const params = new URLSearchParams({
        input: input,
        key: this.apiKey,
        sessiontoken: this.generateSessionToken(),
        language: 'fr',
      });

      // Filtrer par pays (Côte d'Ivoire par défaut)
      if (options.country) {
        params.append('components', `country:${options.country}`);
      } else {
        params.append('components', 'country:ci'); // Côte d'Ivoire
      }

      // Types d'adresses
      if (options.types && options.types.length > 0) {
        params.append('types', options.types.join('|'));
      } else {
        params.append('types', 'address|establishment');
      }

      // Localisation pour biais géographique
      if (options.location) {
        params.append('location', `${options.location.latitude},${options.location.longitude}`);
        if (options.radius) {
          params.append('radius', options.radius.toString());
        }
      }

      const response = await fetch(
        `${this.baseUrl}/autocomplete/json?${params.toString()}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
        throw new Error(`Google Places API error: ${data.status} - ${data.error_message}`);
      }

      const suggestions: AddressSuggestion[] = (data.predictions || []).map((prediction: any) => ({
        id: prediction.place_id,
        description: prediction.description,
        mainText: prediction.structured_formatting?.main_text || prediction.description,
        secondaryText: prediction.structured_formatting?.secondary_text || '',
        placeId: prediction.place_id,
        types: prediction.types || [],
      }));

      console.log(`✅ ${suggestions.length} suggestions trouvées`);
      return suggestions;
    } catch (error) {
      console.error('❌ Erreur suggestions adresse:', error);
      return [];
    }
  }

  /**
   * Obtenir les détails complets d'une adresse
   */
  async getAddressDetails(placeId: string): Promise<AddressDetails | null> {
    try {
      console.log('📍 Récupération détails adresse:', placeId);

      const params = new URLSearchParams({
        place_id: placeId,
        key: this.apiKey,
        sessiontoken: this.generateSessionToken(),
        language: 'fr',
        fields: 'address_components,formatted_address,geometry,name,types',
      });

      const response = await fetch(
        `${this.baseUrl}/details/json?${params.toString()}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== 'OK') {
        throw new Error(`Google Places API error: ${data.status} - ${data.error_message}`);
      }

      const place = data.result;
      
      // Réinitialiser le token après utilisation
      this.resetSessionToken();

      const addressDetails: AddressDetails = {
        placeId: placeId,
        formattedAddress: place.formatted_address,
        coordinates: {
          latitude: place.geometry.location.lat,
          longitude: place.geometry.location.lng,
        },
        addressComponents: place.address_components.map((component: any) => ({
          longName: component.long_name,
          shortName: component.short_name,
          types: component.types,
        })),
      };

      // Extraire les composants d'adresse
      place.address_components.forEach((component: any) => {
        const types = component.types;
        
        if (types.includes('street_number')) {
          addressDetails.streetNumber = component.long_name;
        } else if (types.includes('route')) {
          addressDetails.streetName = component.long_name;
        } else if (types.includes('locality')) {
          addressDetails.locality = component.long_name;
        } else if (types.includes('sublocality') || types.includes('sublocality_level_1')) {
          addressDetails.sublocality = component.long_name;
        } else if (types.includes('administrative_area_level_1')) {
          addressDetails.administrativeAreaLevel1 = component.long_name;
        } else if (types.includes('administrative_area_level_2')) {
          addressDetails.administrativeAreaLevel2 = component.long_name;
        } else if (types.includes('country')) {
          addressDetails.country = component.long_name;
        } else if (types.includes('postal_code')) {
          addressDetails.postalCode = component.long_name;
        }
      });

      console.log('✅ Détails adresse récupérés');
      return addressDetails;
    } catch (error) {
      console.error('❌ Erreur détails adresse:', error);
      return null;
    }
  }

  /**
   * Valider une adresse
   */
  async validateAddress(address: string): Promise<AddressValidationResult> {
    try {
      console.log('✅ Validation adresse:', address);

      // Obtenir des suggestions pour l'adresse
      const suggestions = await this.getAddressSuggestions(address);

      if (suggestions.length === 0) {
        return {
          isValid: false,
          confidence: 'low',
          issues: ['Aucune adresse correspondante trouvée'],
        };
      }

      // Vérifier si l'adresse correspond exactement à une suggestion
      const exactMatch = suggestions.find(
        suggestion => suggestion.description.toLowerCase() === address.toLowerCase()
      );

      if (exactMatch) {
        const details = await this.getAddressDetails(exactMatch.placeId);
        return {
          isValid: true,
          confidence: 'high',
          correctedAddress: details || undefined,
        };
      }

      // Vérifier si l'adresse est similaire à une suggestion
      const similarMatch = suggestions.find(
        suggestion => this.calculateSimilarity(suggestion.description, address) > 0.8
      );

      if (similarMatch) {
        const details = await this.getAddressDetails(similarMatch.placeId);
        return {
          isValid: true,
          confidence: 'medium',
          correctedAddress: details || undefined,
          suggestions: suggestions.slice(0, 3),
        };
      }

      return {
        isValid: false,
        confidence: 'low',
        suggestions: suggestions.slice(0, 5),
        issues: ['Adresse non trouvée, suggestions disponibles'],
      };
    } catch (error) {
      console.error('❌ Erreur validation adresse:', error);
      return {
        isValid: false,
        confidence: 'low',
        issues: ['Erreur lors de la validation'],
      };
    }
  }

  /**
   * Calculer la similarité entre deux chaînes
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) {
      return 1.0;
    }
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Calculer la distance de Levenshtein
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * Géocoder une adresse (obtenir les coordonnées)
   */
  async geocodeAddress(address: string): Promise<{ latitude: number; longitude: number } | null> {
    try {
      const params = new URLSearchParams({
        address: address,
        key: this.apiKey,
        language: 'fr',
      });

      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?${params.toString()}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== 'OK' || !data.results || data.results.length === 0) {
        return null;
      }

      const location = data.results[0].geometry.location;
      return {
        latitude: location.lat,
        longitude: location.lng,
      };
    } catch (error) {
      console.error('❌ Erreur géocodage:', error);
      return null;
    }
  }

  /**
   * Géocodage inverse (obtenir l'adresse depuis les coordonnées)
   */
  async reverseGeocode(latitude: number, longitude: number): Promise<AddressDetails | null> {
    try {
      const params = new URLSearchParams({
        latlng: `${latitude},${longitude}`,
        key: this.apiKey,
        language: 'fr',
        result_type: 'street_address|route|neighborhood|locality',
      });

      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?${params.toString()}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== 'OK' || !data.results || data.results.length === 0) {
        return null;
      }

      const result = data.results[0];
      
      const addressDetails: AddressDetails = {
        placeId: result.place_id,
        formattedAddress: result.formatted_address,
        coordinates: { latitude, longitude },
        addressComponents: result.address_components.map((component: any) => ({
          longName: component.long_name,
          shortName: component.short_name,
          types: component.types,
        })),
      };

      // Extraire les composants comme dans getAddressDetails
      result.address_components.forEach((component: any) => {
        const types = component.types;
        
        if (types.includes('street_number')) {
          addressDetails.streetNumber = component.long_name;
        } else if (types.includes('route')) {
          addressDetails.streetName = component.long_name;
        } else if (types.includes('locality')) {
          addressDetails.locality = component.long_name;
        } else if (types.includes('sublocality')) {
          addressDetails.sublocality = component.long_name;
        } else if (types.includes('administrative_area_level_1')) {
          addressDetails.administrativeAreaLevel1 = component.long_name;
        } else if (types.includes('country')) {
          addressDetails.country = component.long_name;
        } else if (types.includes('postal_code')) {
          addressDetails.postalCode = component.long_name;
        }
      });

      return addressDetails;
    } catch (error) {
      console.error('❌ Erreur géocodage inverse:', error);
      return null;
    }
  }

  /**
   * Formater une adresse pour l'affichage
   */
  formatAddressForDisplay(details: AddressDetails): string {
    const parts = [];
    
    if (details.streetNumber && details.streetName) {
      parts.push(`${details.streetNumber} ${details.streetName}`);
    } else if (details.streetName) {
      parts.push(details.streetName);
    }
    
    if (details.sublocality) {
      parts.push(details.sublocality);
    }
    
    if (details.locality) {
      parts.push(details.locality);
    }
    
    if (details.postalCode) {
      parts.push(details.postalCode);
    }
    
    return parts.join(', ');
  }
}

export const addressAutocompleteService = new AddressAutocompleteService();
export default addressAutocompleteService;
