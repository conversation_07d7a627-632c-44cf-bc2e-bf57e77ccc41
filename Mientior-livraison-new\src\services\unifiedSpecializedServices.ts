// Service unifié pour les services spécialisés avec Supabase en priorité et fallback statique
import { supabaseSpecializedServices } from './supabaseSpecializedServices';
import { dynamicServicesLoader } from './dynamicServicesLoader';
import { migrationService } from './migrationService';
import { SpecializedServiceCategory, SpecializedService } from '../types/specializedServices';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ServiceLoadOptions {
  forceRefresh?: boolean;
  useSupabaseOnly?: boolean;
  useStaticFallback?: boolean;
}

export interface PriceCalculationRequest {
  serviceId: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  distance?: number;
  itemValue?: number;
  selectedOptions?: string[];
  urgentDelivery?: boolean;
  pickupRequired?: boolean;
}

class UnifiedSpecializedServices {
  private readonly PREFERENCE_KEY = '@service_preference';
  private useSupabase: boolean = true;
  private isInitialized: boolean = false;

  /**
   * Initialiser le service unifié
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🔄 Initialisation du service unifié...');

      // Vérifier les préférences utilisateur
      await this.loadUserPreferences();

      // Exécuter la migration automatique si nécessaire
      await migrationService.autoMigrate();

      // Tester la connectivité Supabase
      if (this.useSupabase) {
        const isSupabaseAvailable = await this.testSupabaseConnectivity();
        if (!isSupabaseAvailable) {
          console.warn('⚠️ Supabase non disponible, basculement vers les données statiques');
          this.useSupabase = false;
        }
      }

      this.isInitialized = true;
      console.log(`✅ Service unifié initialisé (Mode: ${this.useSupabase ? 'Supabase' : 'Statique'})`);
    } catch (error) {
      console.error('❌ Erreur initialisation service unifié:', error);
      this.useSupabase = false;
      this.isInitialized = true;
    }
  }

  /**
   * Charger toutes les catégories de services
   */
  async loadServiceCategories(options: ServiceLoadOptions = {}): Promise<SpecializedServiceCategory[]> {
    await this.initialize();

    try {
      if (this.useSupabase && !options.useStaticFallback) {
        console.log('📦 Chargement catégories depuis Supabase...');
        const categories = await supabaseSpecializedServices.loadServiceCategories(options.forceRefresh);
        
        if (categories.length > 0) {
          return this.transformCategoriesToClientFormat(categories);
        } else if (!options.useSupabaseOnly) {
          console.warn('⚠️ Aucune catégorie Supabase, fallback vers statique');
          return this.loadCategoriesFromStatic(options);
        }
      }

      return this.loadCategoriesFromStatic(options);
    } catch (error) {
      console.error('❌ Erreur chargement catégories:', error);
      
      if (!options.useSupabaseOnly) {
        console.log('🔄 Fallback vers données statiques...');
        return this.loadCategoriesFromStatic(options);
      }
      
      throw error;
    }
  }

  /**
   * Charger les services pour une catégorie
   */
  async loadServicesForCategory(categoryId: string, options: ServiceLoadOptions = {}): Promise<SpecializedService[]> {
    await this.initialize();

    try {
      if (this.useSupabase && !options.useStaticFallback) {
        console.log(`🔧 Chargement services Supabase pour catégorie ${categoryId}...`);
        const services = await supabaseSpecializedServices.loadServicesForCategory(categoryId, options.forceRefresh);
        
        if (services.length > 0) {
          return services;
        } else if (!options.useSupabaseOnly) {
          console.warn('⚠️ Aucun service Supabase, fallback vers statique');
          return this.loadServicesFromStatic(categoryId, options);
        }
      }

      return this.loadServicesFromStatic(categoryId, options);
    } catch (error) {
      console.error('❌ Erreur chargement services:', error);
      
      if (!options.useSupabaseOnly) {
        console.log('🔄 Fallback vers données statiques...');
        return this.loadServicesFromStatic(categoryId, options);
      }
      
      return [];
    }
  }

  /**
   * Rechercher des services
   */
  async searchServices(query: string, filters: any = {}): Promise<SpecializedService[]> {
    await this.initialize();

    try {
      if (this.useSupabase) {
        console.log(`🔍 Recherche Supabase: "${query}"`);
        const results = await supabaseSpecializedServices.searchServices({
          search_query: query,
          ...filters,
        });
        
        if (results.length > 0) {
          return results;
        }
      }

      // Fallback vers recherche statique
      console.log('🔄 Fallback recherche statique...');
      return this.searchServicesInStatic(query, filters);
    } catch (error) {
      console.error('❌ Erreur recherche services:', error);
      return this.searchServicesInStatic(query, filters);
    }
  }

  /**
   * Calculer le prix d'un service
   */
  async calculateServicePrice(request: PriceCalculationRequest): Promise<any> {
    await this.initialize();

    try {
      if (this.useSupabase) {
        console.log('💰 Calcul prix Supabase...');
        return await supabaseSpecializedServices.calculateServicePrice({
          service_id: request.serviceId,
          weight_kg: request.weight,
          dimensions: request.dimensions,
          distance_km: request.distance,
          item_value: request.itemValue,
          selected_options: request.selectedOptions,
          urgent_delivery: request.urgentDelivery,
          pickup_required: request.pickupRequired,
        });
      }

      // Fallback vers calcul statique
      console.log('🔄 Fallback calcul prix statique...');
      return this.calculatePriceFromStatic(request);
    } catch (error) {
      console.error('❌ Erreur calcul prix:', error);
      return this.calculatePriceFromStatic(request);
    }
  }

  /**
   * Obtenir un service par ID
   */
  async getServiceById(serviceId: string): Promise<SpecializedService | null> {
    await this.initialize();

    try {
      if (this.useSupabase) {
        const service = await supabaseSpecializedServices.getServiceById(serviceId);
        if (service) return service;
      }

      // Fallback vers données statiques
      return this.getServiceFromStatic(serviceId);
    } catch (error) {
      console.error('❌ Erreur récupération service:', error);
      return this.getServiceFromStatic(serviceId);
    }
  }

  /**
   * S'abonner aux mises à jour en temps réel
   */
  subscribeToUpdates(callback: (payload: any) => void): () => void {
    if (this.useSupabase) {
      return supabaseSpecializedServices.subscribeToUpdates(callback);
    }
    
    // Retourner une fonction vide pour le mode statique
    return () => {};
  }

  /**
   * Basculer vers Supabase
   */
  async switchToSupabase(): Promise<boolean> {
    try {
      const isAvailable = await this.testSupabaseConnectivity();
      if (isAvailable) {
        this.useSupabase = true;
        await this.saveUserPreferences();
        await migrationService.switchToSupabaseService();
        console.log('✅ Basculement vers Supabase effectué');
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ Erreur basculement Supabase:', error);
      return false;
    }
  }

  /**
   * Basculer vers les données statiques
   */
  async switchToStatic(): Promise<void> {
    this.useSupabase = false;
    await this.saveUserPreferences();
    console.log('✅ Basculement vers données statiques effectué');
  }

  /**
   * Obtenir le mode actuel
   */
  getCurrentMode(): 'supabase' | 'static' {
    return this.useSupabase ? 'supabase' : 'static';
  }

  /**
   * Vider tous les caches
   */
  async clearAllCaches(): Promise<void> {
    try {
      supabaseSpecializedServices.clearCache();
      await dynamicServicesLoader.clearCache();
      console.log('✅ Tous les caches vidés');
    } catch (error) {
      console.error('❌ Erreur vidage caches:', error);
    }
  }

  // =====================================================
  // MÉTHODES PRIVÉES
  // =====================================================

  /**
   * Tester la connectivité Supabase
   */
  private async testSupabaseConnectivity(): Promise<boolean> {
    try {
      const categories = await supabaseSpecializedServices.loadServiceCategories(true);
      return categories.length > 0;
    } catch (error) {
      console.warn('⚠️ Test connectivité Supabase échoué:', error);
      return false;
    }
  }

  /**
   * Charger les catégories depuis les données statiques
   */
  private async loadCategoriesFromStatic(options: ServiceLoadOptions): Promise<SpecializedServiceCategory[]> {
    const categories = await dynamicServicesLoader.loadServiceCategories(options.forceRefresh);
    return this.transformCategoriesToClientFormat(categories);
  }

  /**
   * Charger les services depuis les données statiques
   */
  private async loadServicesFromStatic(categoryId: string, options: ServiceLoadOptions): Promise<SpecializedService[]> {
    const categories = await dynamicServicesLoader.loadServiceCategories(options.forceRefresh);
    const category = categories.find(c => c.id === categoryId);
    return category?.services || [];
  }

  /**
   * Rechercher dans les données statiques
   */
  private async searchServicesInStatic(query: string, filters: any): Promise<SpecializedService[]> {
    const categories = await dynamicServicesLoader.loadServiceCategories();
    const allServices: SpecializedService[] = [];
    
    categories.forEach(category => {
      category.services.forEach(service => {
        if (service.name.toLowerCase().includes(query.toLowerCase()) ||
            service.description.toLowerCase().includes(query.toLowerCase())) {
          allServices.push(service);
        }
      });
    });
    
    return allServices;
  }

  /**
   * Calculer le prix depuis les données statiques
   */
  private async calculatePriceFromStatic(request: PriceCalculationRequest): Promise<any> {
    // Implémentation simplifiée pour le fallback
    return {
      basePrice: 1500,
      totalPrice: 1500,
      currency: 'XOF',
      breakdown: {
        basePrice: 1500,
        additionalFees: 0,
        taxes: 0,
      },
    };
  }

  /**
   * Obtenir un service depuis les données statiques
   */
  private async getServiceFromStatic(serviceId: string): Promise<SpecializedService | null> {
    const categories = await dynamicServicesLoader.loadServiceCategories();
    
    for (const category of categories) {
      const service = category.services.find(s => s.id === serviceId);
      if (service) return service;
    }
    
    return null;
  }

  /**
   * Transformer les catégories pour le format client (sans données de rentabilité)
   */
  private transformCategoriesToClientFormat(categories: SpecializedServiceCategory[]): SpecializedServiceCategory[] {
    return categories.map(category => ({
      ...category,
      // Remplacer les métriques de rentabilité par des métriques de valeur client
      customerValue: 'premium',
      valueProposition: this.getValueProposition(category.name),
      customerSatisfaction: this.getCustomerSatisfaction(category.name),
    }));
  }

  /**
   * Obtenir la proposition de valeur pour une catégorie
   */
  private getValueProposition(categoryName: string): string {
    const valueMap: { [key: string]: string } = {
      'Colis & Marchandises': 'Sécurisé',
      'Pharmacie & Santé': 'Santé',
      'Boulangerie & Produits Frais': 'Fraîcheur',
      'Pressing & Nettoyage': 'Premium',
      'Électronique & Technologie': 'Sécurisé',
      'Librairie & Papeterie': 'Éducation',
      'Beauté & Coiffure': 'Domicile',
      'Restaurant & Traiteur': 'Qualité',
    };
    
    return valueMap[categoryName] || 'Qualité';
  }

  /**
   * Obtenir le taux de satisfaction client pour une catégorie
   */
  private getCustomerSatisfaction(categoryName: string): number {
    const satisfactionMap: { [key: string]: number } = {
      'Colis & Marchandises': 88,
      'Pharmacie & Santé': 92,
      'Boulangerie & Produits Frais': 87,
      'Pressing & Nettoyage': 90,
      'Électronique & Technologie': 93,
      'Librairie & Papeterie': 85,
      'Beauté & Coiffure': 95,
      'Restaurant & Traiteur': 89,
    };
    
    return satisfactionMap[categoryName] || 90;
  }

  /**
   * Charger les préférences utilisateur
   */
  private async loadUserPreferences(): Promise<void> {
    try {
      const preferences = await AsyncStorage.getItem(this.PREFERENCE_KEY);
      if (preferences) {
        const { useSupabase } = JSON.parse(preferences);
        this.useSupabase = useSupabase !== false; // Par défaut true
      }
    } catch (error) {
      console.warn('⚠️ Erreur chargement préférences:', error);
    }
  }

  /**
   * Sauvegarder les préférences utilisateur
   */
  private async saveUserPreferences(): Promise<void> {
    try {
      const preferences = { useSupabase: this.useSupabase };
      await AsyncStorage.setItem(this.PREFERENCE_KEY, JSON.stringify(preferences));
    } catch (error) {
      console.warn('⚠️ Erreur sauvegarde préférences:', error);
    }
  }
}

export const unifiedSpecializedServices = new UnifiedSpecializedServices();
export default unifiedSpecializedServices;
