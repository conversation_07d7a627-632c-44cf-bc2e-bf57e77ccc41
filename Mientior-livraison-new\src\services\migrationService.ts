// Service de migration des données statiques vers Supabase
import { supabaseSpecializedServices } from './supabaseSpecializedServices';
import { dynamicServicesLoader } from './dynamicServicesLoader';
import { SpecializedServiceCategory, SpecializedService } from '../types/specializedServices';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface MigrationStatus {
  isCompleted: boolean;
  lastMigrationDate: string;
  migratedCategories: number;
  migratedServices: number;
  errors: string[];
}

class MigrationService {
  private readonly MIGRATION_KEY = '@migration_status';
  private readonly MIGRATION_VERSION = '1.0.0';

  /**
   * Vérifier si la migration est nécessaire
   */
  async isMigrationNeeded(): Promise<boolean> {
    try {
      const statusJson = await AsyncStorage.getItem(this.MIGRATION_KEY);
      if (!statusJson) return true;

      const status: MigrationStatus = JSON.parse(statusJson);
      return !status.isCompleted;
    } catch (error) {
      console.error('❌ Erreur vérification migration:', error);
      return true;
    }
  }

  /**
   * Exécuter la migration complète
   */
  async executeMigration(): Promise<MigrationStatus> {
    console.log('🔄 Début de la migration vers Supabase...');
    
    const status: MigrationStatus = {
      isCompleted: false,
      lastMigrationDate: new Date().toISOString(),
      migratedCategories: 0,
      migratedServices: 0,
      errors: [],
    };

    try {
      // Étape 1: Migrer les catégories
      console.log('📦 Migration des catégories...');
      await this.migrateCategoriesFromStatic();
      status.migratedCategories = await this.countMigratedCategories();

      // Étape 2: Migrer les services
      console.log('🔧 Migration des services...');
      await this.migrateServicesFromStatic();
      status.migratedServices = await this.countMigratedServices();

      // Étape 3: Valider la migration
      console.log('✅ Validation de la migration...');
      const isValid = await this.validateMigration();
      
      if (isValid) {
        status.isCompleted = true;
        console.log('🎉 Migration terminée avec succès!');
      } else {
        status.errors.push('Validation de la migration échouée');
        console.error('❌ Validation de la migration échouée');
      }

      // Sauvegarder le statut
      await AsyncStorage.setItem(this.MIGRATION_KEY, JSON.stringify(status));

      return status;
    } catch (error) {
      console.error('❌ Erreur lors de la migration:', error);
      status.errors.push(`Erreur migration: ${error}`);
      await AsyncStorage.setItem(this.MIGRATION_KEY, JSON.stringify(status));
      return status;
    }
  }

  /**
   * Migrer les catégories depuis les données statiques
   */
  private async migrateCategoriesFromStatic(): Promise<void> {
    try {
      // Charger les catégories depuis le service dynamique existant
      const staticCategories = await dynamicServicesLoader.loadServiceCategories();
      
      console.log(`📦 ${staticCategories.length} catégories trouvées dans les données statiques`);

      // Les données sont déjà dans Supabase via les migrations SQL
      // Cette fonction sert principalement à valider que les données sont présentes
      const supabaseCategories = await supabaseSpecializedServices.loadServiceCategories(true);
      
      console.log(`✅ ${supabaseCategories.length} catégories disponibles dans Supabase`);
    } catch (error) {
      console.error('❌ Erreur migration catégories:', error);
      throw error;
    }
  }

  /**
   * Migrer les services depuis les données statiques
   */
  private async migrateServicesFromStatic(): Promise<void> {
    try {
      // Charger toutes les catégories pour obtenir leurs services
      const categories = await supabaseSpecializedServices.loadServiceCategories(true);
      
      let totalServices = 0;
      for (const category of categories) {
        const services = await supabaseSpecializedServices.loadServicesForCategory(category.id, true);
        totalServices += services.length;
        console.log(`✅ ${services.length} services migrés pour ${category.name}`);
      }

      console.log(`✅ Total: ${totalServices} services disponibles dans Supabase`);
    } catch (error) {
      console.error('❌ Erreur migration services:', error);
      throw error;
    }
  }

  /**
   * Valider que la migration s'est bien passée
   */
  private async validateMigration(): Promise<boolean> {
    try {
      // Vérifier que les catégories sont disponibles
      const categories = await supabaseSpecializedServices.loadServiceCategories();
      if (categories.length === 0) {
        console.error('❌ Aucune catégorie trouvée après migration');
        return false;
      }

      // Vérifier que chaque catégorie a des services
      for (const category of categories) {
        const services = await supabaseSpecializedServices.loadServicesForCategory(category.id);
        if (services.length === 0) {
          console.warn(`⚠️ Aucun service trouvé pour la catégorie ${category.name}`);
        }
      }

      // Tester une recherche de services
      const searchResults = await supabaseSpecializedServices.searchServices({
        search_query: 'livraison',
      });

      if (searchResults.length === 0) {
        console.warn('⚠️ Aucun résultat de recherche trouvé');
      }

      console.log('✅ Validation de la migration réussie');
      return true;
    } catch (error) {
      console.error('❌ Erreur validation migration:', error);
      return false;
    }
  }

  /**
   * Compter les catégories migrées
   */
  private async countMigratedCategories(): Promise<number> {
    try {
      const categories = await supabaseSpecializedServices.loadServiceCategories();
      return categories.length;
    } catch (error) {
      console.error('❌ Erreur comptage catégories:', error);
      return 0;
    }
  }

  /**
   * Compter les services migrés
   */
  private async countMigratedServices(): Promise<number> {
    try {
      const categories = await supabaseSpecializedServices.loadServiceCategories();
      let totalServices = 0;
      
      for (const category of categories) {
        const services = await supabaseSpecializedServices.loadServicesForCategory(category.id);
        totalServices += services.length;
      }
      
      return totalServices;
    } catch (error) {
      console.error('❌ Erreur comptage services:', error);
      return 0;
    }
  }

  /**
   * Obtenir le statut de la migration
   */
  async getMigrationStatus(): Promise<MigrationStatus | null> {
    try {
      const statusJson = await AsyncStorage.getItem(this.MIGRATION_KEY);
      if (!statusJson) return null;

      return JSON.parse(statusJson);
    } catch (error) {
      console.error('❌ Erreur récupération statut migration:', error);
      return null;
    }
  }

  /**
   * Réinitialiser la migration (pour les tests)
   */
  async resetMigration(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.MIGRATION_KEY);
      console.log('✅ Migration réinitialisée');
    } catch (error) {
      console.error('❌ Erreur réinitialisation migration:', error);
    }
  }

  /**
   * Migrer automatiquement au démarrage de l'application
   */
  async autoMigrate(): Promise<void> {
    try {
      const needsMigration = await this.isMigrationNeeded();
      
      if (needsMigration) {
        console.log('🔄 Migration automatique nécessaire...');
        const status = await this.executeMigration();
        
        if (status.isCompleted) {
          console.log('✅ Migration automatique terminée');
        } else {
          console.error('❌ Migration automatique échouée:', status.errors);
        }
      } else {
        console.log('✅ Migration déjà effectuée');
      }
    } catch (error) {
      console.error('❌ Erreur migration automatique:', error);
    }
  }

  /**
   * Basculer vers le service Supabase
   */
  async switchToSupabaseService(): Promise<void> {
    try {
      // Vider le cache du service statique
      await dynamicServicesLoader.clearCache();
      
      // Vider le cache du service Supabase pour forcer un rechargement
      supabaseSpecializedServices.clearCache();
      
      // Précharger les données Supabase
      await supabaseSpecializedServices.loadServiceCategories(true);
      
      console.log('✅ Basculement vers Supabase effectué');
    } catch (error) {
      console.error('❌ Erreur basculement Supabase:', error);
      throw error;
    }
  }

  /**
   * Générer un rapport de migration
   */
  async generateMigrationReport(): Promise<string> {
    try {
      const status = await this.getMigrationStatus();
      
      if (!status) {
        return '❌ Aucune information de migration disponible';
      }

      let report = '📋 RAPPORT DE MIGRATION SUPABASE\n\n';
      
      report += `🎯 Statut: ${status.isCompleted ? '✅ Terminée' : '⚠️ En cours/Échouée'}\n`;
      report += `📅 Date: ${new Date(status.lastMigrationDate).toLocaleString()}\n`;
      report += `📦 Catégories migrées: ${status.migratedCategories}\n`;
      report += `🔧 Services migrés: ${status.migratedServices}\n\n`;
      
      if (status.errors.length > 0) {
        report += '❌ ERREURS:\n';
        status.errors.forEach(error => {
          report += `• ${error}\n`;
        });
        report += '\n';
      }
      
      // Ajouter des statistiques en temps réel
      try {
        const categories = await supabaseSpecializedServices.loadServiceCategories();
        report += '📊 STATISTIQUES ACTUELLES:\n';
        report += `• ${categories.length} catégories actives\n`;
        
        let totalServices = 0;
        for (const category of categories) {
          const services = await supabaseSpecializedServices.loadServicesForCategory(category.id);
          totalServices += services.length;
          report += `• ${category.name}: ${services.length} services\n`;
        }
        
        report += `• Total: ${totalServices} services disponibles\n\n`;
      } catch (error) {
        report += '⚠️ Impossible de récupérer les statistiques actuelles\n\n';
      }
      
      report += '🚀 MIGRATION VERS SUPABASE TERMINÉE\n';
      report += 'Les services dynamiques utilisent maintenant la base de données Supabase\n';
      report += 'pour un chargement en temps réel et une meilleure performance.';
      
      return report;
    } catch (error) {
      return `❌ Erreur génération rapport: ${error}`;
    }
  }
}

export const migrationService = new MigrationService();
export default migrationService;
