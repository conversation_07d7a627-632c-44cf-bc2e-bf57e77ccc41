// Service d'auto-complétion intelligente des formulaires
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase';
import { AddressDetails } from './addressAutocompleteService';
import { SpecializedService } from '../types/specializedServices';

export interface UserAddress {
  id: string;
  label: string; // 'Domicile', 'Bureau', 'Autre'
  addressDetails: AddressDetails;
  isDefault: boolean;
  usageCount: number;
  lastUsed: Date;
  isActive: boolean;
}

export interface UserPreferences {
  defaultDeliveryAddress?: UserAddress;
  defaultPickupAddress?: UserAddress;
  preferredDeliveryTime: 'morning' | 'afternoon' | 'evening' | 'anytime';
  preferredServices: string[]; // IDs des services préférés
  frequentOptions: string[]; // Options fréquemment sélectionnées
  contactPreferences: {
    primaryPhone: string;
    alternativePhone?: string;
    preferredContactMethod: 'sms' | 'call' | 'whatsapp';
  };
  paymentPreferences: {
    preferredMethod: 'cash' | 'mobile_money' | 'card';
    mobileMoneyNumber?: string;
  };
  deliveryInstructions: string[];
  subscriptions: {
    serviceId: string;
    planId: string;
    isActive: boolean;
  }[];
}

export interface OrderHistory {
  serviceId: string;
  categoryId: string;
  deliveryAddress: AddressDetails;
  pickupAddress?: AddressDetails;
  selectedOptions: string[];
  orderValue: number;
  frequency: number;
  lastOrderDate: Date;
  averageRating: number;
}

export interface SmartSuggestion {
  type: 'address' | 'service' | 'option' | 'time' | 'instruction';
  value: any;
  confidence: number;
  reason: string;
  usageCount?: number;
}

export interface FormAutocompletionData {
  suggestedAddresses: UserAddress[];
  suggestedServices: SpecializedService[];
  suggestedOptions: string[];
  suggestedInstructions: string[];
  smartSuggestions: SmartSuggestion[];
  prefillData: {
    deliveryAddress?: AddressDetails;
    pickupAddress?: AddressDetails;
    contactPhone?: string;
    alternativeContact?: string;
    preferredTime?: string;
    commonInstructions?: string;
  };
}

class SmartFormAutocompletion {
  private userPreferences: UserPreferences | null = null;
  private userAddresses: UserAddress[] = [];
  private orderHistory: OrderHistory[] = [];
  private preferencesKey = 'user_preferences';
  private addressesKey = 'user_addresses';

  /**
   * Initialiser les données utilisateur
   */
  async initializeUserData(userId: string): Promise<void> {
    try {
      console.log('🔄 Initialisation données utilisateur pour auto-complétion');

      await Promise.all([
        this.loadUserPreferences(userId),
        this.loadUserAddresses(userId),
        this.loadOrderHistory(userId),
      ]);

      console.log('✅ Données utilisateur initialisées');
    } catch (error) {
      console.error('❌ Erreur initialisation données utilisateur:', error);
    }
  }

  /**
   * Obtenir les données d'auto-complétion pour un formulaire
   */
  async getFormAutocompletionData(
    serviceId?: string,
    categoryId?: string
  ): Promise<FormAutocompletionData> {
    try {
      console.log('🎯 Génération données auto-complétion');

      const suggestedAddresses = this.getSuggestedAddresses();
      const suggestedServices = await this.getSuggestedServices(categoryId);
      const suggestedOptions = this.getSuggestedOptions(serviceId);
      const suggestedInstructions = this.getSuggestedInstructions();
      const smartSuggestions = this.generateSmartSuggestions(serviceId, categoryId);
      const prefillData = this.generatePrefillData(serviceId);

      return {
        suggestedAddresses,
        suggestedServices,
        suggestedOptions,
        suggestedInstructions,
        smartSuggestions,
        prefillData,
      };
    } catch (error) {
      console.error('❌ Erreur génération auto-complétion:', error);
      return {
        suggestedAddresses: [],
        suggestedServices: [],
        suggestedOptions: [],
        suggestedInstructions: [],
        smartSuggestions: [],
        prefillData: {},
      };
    }
  }

  /**
   * Sauvegarder une nouvelle adresse
   */
  async saveUserAddress(
    userId: string,
    addressDetails: AddressDetails,
    label: string,
    isDefault: boolean = false
  ): Promise<UserAddress> {
    try {
      const newAddress: UserAddress = {
        id: `addr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        label,
        addressDetails,
        isDefault,
        usageCount: 1,
        lastUsed: new Date(),
        isActive: true,
      };

      // Si c'est l'adresse par défaut, désactiver les autres
      if (isDefault) {
        this.userAddresses.forEach(addr => addr.isDefault = false);
      }

      this.userAddresses.push(newAddress);
      await this.saveUserAddresses(userId);

      // Sauvegarder en base de données
      await supabase
        .from('user_addresses')
        .insert({
          id: newAddress.id,
          user_id: userId,
          label: newAddress.label,
          address_details: newAddress.addressDetails,
          is_default: newAddress.isDefault,
          usage_count: newAddress.usageCount,
          last_used: newAddress.lastUsed.toISOString(),
          is_active: newAddress.isActive,
        });

      console.log('✅ Adresse utilisateur sauvegardée');
      return newAddress;
    } catch (error) {
      console.error('❌ Erreur sauvegarde adresse:', error);
      throw error;
    }
  }

  /**
   * Mettre à jour les préférences utilisateur
   */
  async updateUserPreferences(
    userId: string,
    preferences: Partial<UserPreferences>
  ): Promise<void> {
    try {
      this.userPreferences = {
        ...this.userPreferences,
        ...preferences,
      } as UserPreferences;

      await this.saveUserPreferences(userId);

      // Sauvegarder en base de données
      await supabase
        .from('user_preferences')
        .upsert({
          user_id: userId,
          preferences: this.userPreferences,
          updated_at: new Date().toISOString(),
        });

      console.log('✅ Préférences utilisateur mises à jour');
    } catch (error) {
      console.error('❌ Erreur mise à jour préférences:', error);
    }
  }

  /**
   * Enregistrer l'utilisation d'une adresse
   */
  async recordAddressUsage(userId: string, addressId: string): Promise<void> {
    try {
      const address = this.userAddresses.find(addr => addr.id === addressId);
      if (address) {
        address.usageCount++;
        address.lastUsed = new Date();
        await this.saveUserAddresses(userId);

        // Mettre à jour en base de données
        await supabase
          .from('user_addresses')
          .update({
            usage_count: address.usageCount,
            last_used: address.lastUsed.toISOString(),
          })
          .eq('id', addressId);
      }
    } catch (error) {
      console.error('❌ Erreur enregistrement utilisation adresse:', error);
    }
  }

  /**
   * Enregistrer l'utilisation d'un service
   */
  async recordServiceUsage(
    userId: string,
    serviceId: string,
    categoryId: string,
    selectedOptions: string[],
    orderValue: number
  ): Promise<void> {
    try {
      const existingHistory = this.orderHistory.find(h => h.serviceId === serviceId);
      
      if (existingHistory) {
        existingHistory.frequency++;
        existingHistory.lastOrderDate = new Date();
        existingHistory.selectedOptions = [...new Set([...existingHistory.selectedOptions, ...selectedOptions])];
      } else {
        this.orderHistory.push({
          serviceId,
          categoryId,
          deliveryAddress: this.userPreferences?.defaultDeliveryAddress?.addressDetails!,
          selectedOptions,
          orderValue,
          frequency: 1,
          lastOrderDate: new Date(),
          averageRating: 0,
        });
      }

      // Mettre à jour les services préférés
      if (this.userPreferences) {
        if (!this.userPreferences.preferredServices.includes(serviceId)) {
          this.userPreferences.preferredServices.push(serviceId);
        }

        // Ajouter les options fréquentes
        selectedOptions.forEach(option => {
          if (!this.userPreferences!.frequentOptions.includes(option)) {
            this.userPreferences!.frequentOptions.push(option);
          }
        });

        await this.saveUserPreferences(userId);
      }

      console.log('✅ Utilisation service enregistrée');
    } catch (error) {
      console.error('❌ Erreur enregistrement utilisation service:', error);
    }
  }

  /**
   * Obtenir les adresses suggérées
   */
  private getSuggestedAddresses(): UserAddress[] {
    return this.userAddresses
      .filter(addr => addr.isActive)
      .sort((a, b) => {
        // Prioriser l'adresse par défaut
        if (a.isDefault && !b.isDefault) return -1;
        if (!a.isDefault && b.isDefault) return 1;
        
        // Puis par fréquence d'utilisation
        if (a.usageCount !== b.usageCount) {
          return b.usageCount - a.usageCount;
        }
        
        // Puis par date de dernière utilisation
        return new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime();
      })
      .slice(0, 5);
  }

  /**
   * Obtenir les services suggérés
   */
  private async getSuggestedServices(categoryId?: string): Promise<SpecializedService[]> {
    // Cette méthode devrait utiliser le dynamicServicesLoader
    // Pour l'instant, retourner une liste vide
    return [];
  }

  /**
   * Obtenir les options suggérées
   */
  private getSuggestedOptions(serviceId?: string): string[] {
    if (!this.userPreferences) return [];

    // Filtrer les options par service si spécifié
    let options = this.userPreferences.frequentOptions;

    if (serviceId) {
      const serviceHistory = this.orderHistory.find(h => h.serviceId === serviceId);
      if (serviceHistory) {
        options = serviceHistory.selectedOptions;
      }
    }

    return options.slice(0, 5);
  }

  /**
   * Obtenir les instructions suggérées
   */
  private getSuggestedInstructions(): string[] {
    if (!this.userPreferences) return [];

    return this.userPreferences.deliveryInstructions.slice(0, 3);
  }

  /**
   * Générer des suggestions intelligentes
   */
  private generateSmartSuggestions(serviceId?: string, categoryId?: string): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];

    // Suggestion d'adresse par défaut
    if (this.userPreferences?.defaultDeliveryAddress) {
      suggestions.push({
        type: 'address',
        value: this.userPreferences.defaultDeliveryAddress,
        confidence: 0.9,
        reason: 'Votre adresse de livraison habituelle',
        usageCount: this.userPreferences.defaultDeliveryAddress.usageCount,
      });
    }

    // Suggestion de service basée sur l'historique
    if (serviceId) {
      const serviceHistory = this.orderHistory.find(h => h.serviceId === serviceId);
      if (serviceHistory && serviceHistory.frequency > 2) {
        suggestions.push({
          type: 'service',
          value: serviceId,
          confidence: Math.min(0.9, serviceHistory.frequency * 0.1),
          reason: `Vous avez utilisé ce service ${serviceHistory.frequency} fois`,
          usageCount: serviceHistory.frequency,
        });
      }
    }

    // Suggestion d'horaire préféré
    if (this.userPreferences?.preferredDeliveryTime !== 'anytime') {
      suggestions.push({
        type: 'time',
        value: this.userPreferences.preferredDeliveryTime,
        confidence: 0.7,
        reason: 'Votre créneau de livraison préféré',
      });
    }

    return suggestions.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Générer les données de pré-remplissage
   */
  private generatePrefillData(serviceId?: string): FormAutocompletionData['prefillData'] {
    const prefillData: FormAutocompletionData['prefillData'] = {};

    if (this.userPreferences) {
      // Adresse de livraison par défaut
      if (this.userPreferences.defaultDeliveryAddress) {
        prefillData.deliveryAddress = this.userPreferences.defaultDeliveryAddress.addressDetails;
      }

      // Adresse de collecte par défaut
      if (this.userPreferences.defaultPickupAddress) {
        prefillData.pickupAddress = this.userPreferences.defaultPickupAddress.addressDetails;
      }

      // Téléphone de contact
      if (this.userPreferences.contactPreferences.primaryPhone) {
        prefillData.contactPhone = this.userPreferences.contactPreferences.primaryPhone;
      }

      // Contact alternatif
      if (this.userPreferences.contactPreferences.alternativePhone) {
        prefillData.alternativeContact = this.userPreferences.contactPreferences.alternativePhone;
      }

      // Instructions communes
      if (this.userPreferences.deliveryInstructions.length > 0) {
        prefillData.commonInstructions = this.userPreferences.deliveryInstructions[0];
      }
    }

    return prefillData;
  }

  /**
   * Charger les préférences utilisateur
   */
  private async loadUserPreferences(userId: string): Promise<void> {
    try {
      // Charger depuis le cache local
      const cached = await AsyncStorage.getItem(`${this.preferencesKey}_${userId}`);
      if (cached) {
        this.userPreferences = JSON.parse(cached);
      }

      // Charger depuis la base de données
      const { data, error } = await supabase
        .from('user_preferences')
        .select('preferences')
        .eq('user_id', userId)
        .single();

      if (!error && data) {
        this.userPreferences = data.preferences;
        await AsyncStorage.setItem(`${this.preferencesKey}_${userId}`, JSON.stringify(data.preferences));
      }
    } catch (error) {
      console.error('❌ Erreur chargement préférences:', error);
    }
  }

  /**
   * Charger les adresses utilisateur
   */
  private async loadUserAddresses(userId: string): Promise<void> {
    try {
      // Charger depuis le cache local
      const cached = await AsyncStorage.getItem(`${this.addressesKey}_${userId}`);
      if (cached) {
        this.userAddresses = JSON.parse(cached);
      }

      // Charger depuis la base de données
      const { data, error } = await supabase
        .from('user_addresses')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('usage_count', { ascending: false });

      if (!error && data) {
        this.userAddresses = data.map(addr => ({
          id: addr.id,
          label: addr.label,
          addressDetails: addr.address_details,
          isDefault: addr.is_default,
          usageCount: addr.usage_count,
          lastUsed: new Date(addr.last_used),
          isActive: addr.is_active,
        }));

        await AsyncStorage.setItem(`${this.addressesKey}_${userId}`, JSON.stringify(this.userAddresses));
      }
    } catch (error) {
      console.error('❌ Erreur chargement adresses:', error);
    }
  }

  /**
   * Charger l'historique des commandes
   */
  private async loadOrderHistory(userId: string): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('specialized_service_orders')
        .select('service_id, category_id, order_details, pricing')
        .eq('user_id', userId)
        .eq('status', 'delivered')
        .order('created_at', { ascending: false })
        .limit(50);

      if (!error && data) {
        // Traiter les données pour créer l'historique
        const historyMap = new Map<string, OrderHistory>();

        data.forEach(order => {
          const existing = historyMap.get(order.service_id);
          if (existing) {
            existing.frequency++;
            existing.lastOrderDate = new Date(order.created_at);
          } else {
            historyMap.set(order.service_id, {
              serviceId: order.service_id,
              categoryId: order.category_id,
              deliveryAddress: order.order_details.deliveryAddress,
              pickupAddress: order.order_details.pickupAddress,
              selectedOptions: order.order_details.selectedOptions || [],
              orderValue: order.pricing.totalPrice,
              frequency: 1,
              lastOrderDate: new Date(order.created_at),
              averageRating: 0,
            });
          }
        });

        this.orderHistory = Array.from(historyMap.values());
      }
    } catch (error) {
      console.error('❌ Erreur chargement historique:', error);
    }
  }

  /**
   * Sauvegarder les préférences utilisateur
   */
  private async saveUserPreferences(userId: string): Promise<void> {
    try {
      await AsyncStorage.setItem(`${this.preferencesKey}_${userId}`, JSON.stringify(this.userPreferences));
    } catch (error) {
      console.error('❌ Erreur sauvegarde préférences:', error);
    }
  }

  /**
   * Sauvegarder les adresses utilisateur
   */
  private async saveUserAddresses(userId: string): Promise<void> {
    try {
      await AsyncStorage.setItem(`${this.addressesKey}_${userId}`, JSON.stringify(this.userAddresses));
    } catch (error) {
      console.error('❌ Erreur sauvegarde adresses:', error);
    }
  }
}

export const smartFormAutocompletion = new SmartFormAutocompletion();
export default smartFormAutocompletion;
