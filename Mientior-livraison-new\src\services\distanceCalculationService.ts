// Service de calcul automatique de distance avec Google Maps Distance Matrix API
import { AddressDetails } from './addressAutocompleteService';

export interface DistanceCalculationResult {
  distance: {
    text: string;
    value: number; // en mètres
  };
  duration: {
    text: string;
    value: number; // en secondes
  };
  status: 'OK' | 'NOT_FOUND' | 'ZERO_RESULTS' | 'MAX_ROUTE_LENGTH_EXCEEDED' | 'INVALID_REQUEST';
  trafficModel?: 'best_guess' | 'pessimistic' | 'optimistic';
  durationInTraffic?: {
    text: string;
    value: number;
  };
}

export interface RouteInfo {
  origin: AddressDetails | { latitude: number; longitude: number };
  destination: AddressDetails | { latitude: number; longitude: number };
  distance: DistanceCalculationResult;
  estimatedCost?: number;
  routePolyline?: string;
}

export interface DistanceMatrixOptions {
  mode?: 'driving' | 'walking' | 'bicycling' | 'transit';
  avoidTolls?: boolean;
  avoidHighways?: boolean;
  avoidFerries?: boolean;
  trafficModel?: 'best_guess' | 'pessimistic' | 'optimistic';
  departureTime?: Date;
  arrivalTime?: Date;
  units?: 'metric' | 'imperial';
}

class DistanceCalculationService {
  private apiKey: string;
  private baseUrl = 'https://maps.googleapis.com/maps/api/distancematrix';
  private directionsUrl = 'https://maps.googleapis.com/maps/api/directions';
  private cache: Map<string, { result: DistanceCalculationResult; timestamp: number }> = new Map();
  private cacheExpiry = 30 * 60 * 1000; // 30 minutes

  constructor() {
    // En production, cette clé devrait venir des variables d'environnement
    this.apiKey = 'YOUR_GOOGLE_MAPS_API_KEY'; // À remplacer par votre vraie clé
  }

  /**
   * Calculer la distance entre deux points
   */
  async calculateDistance(
    origin: AddressDetails | { latitude: number; longitude: number } | string,
    destination: AddressDetails | { latitude: number; longitude: number } | string,
    options: DistanceMatrixOptions = {}
  ): Promise<DistanceCalculationResult | null> {
    try {
      console.log('📏 Calcul distance entre:', origin, 'et', destination);

      // Créer une clé de cache
      const cacheKey = this.createCacheKey(origin, destination, options);
      
      // Vérifier le cache
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        console.log('✅ Distance récupérée depuis le cache');
        return cached;
      }

      // Formater les coordonnées
      const originStr = this.formatLocation(origin);
      const destinationStr = this.formatLocation(destination);

      if (!originStr || !destinationStr) {
        throw new Error('Coordonnées invalides');
      }

      // Construire les paramètres de l'API
      const params = new URLSearchParams({
        origins: originStr,
        destinations: destinationStr,
        key: this.apiKey,
        language: 'fr',
        units: options.units || 'metric',
        mode: options.mode || 'driving',
      });

      // Options de trafic
      if (options.trafficModel && options.mode === 'driving') {
        params.append('traffic_model', options.trafficModel);
        if (options.departureTime) {
          params.append('departure_time', Math.floor(options.departureTime.getTime() / 1000).toString());
        }
      }

      // Options d'évitement
      const avoid = [];
      if (options.avoidTolls) avoid.push('tolls');
      if (options.avoidHighways) avoid.push('highways');
      if (options.avoidFerries) avoid.push('ferries');
      if (avoid.length > 0) {
        params.append('avoid', avoid.join('|'));
      }

      const response = await fetch(`${this.baseUrl}/json?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== 'OK') {
        throw new Error(`Distance Matrix API error: ${data.status}`);
      }

      if (!data.rows || data.rows.length === 0 || !data.rows[0].elements || data.rows[0].elements.length === 0) {
        throw new Error('Aucun résultat de distance trouvé');
      }

      const element = data.rows[0].elements[0];

      if (element.status !== 'OK') {
        console.warn('⚠️ Statut distance:', element.status);
        return {
          distance: { text: 'N/A', value: 0 },
          duration: { text: 'N/A', value: 0 },
          status: element.status,
        };
      }

      const result: DistanceCalculationResult = {
        distance: element.distance,
        duration: element.duration,
        status: element.status,
        trafficModel: options.trafficModel,
      };

      // Ajouter la durée avec trafic si disponible
      if (element.duration_in_traffic) {
        result.durationInTraffic = element.duration_in_traffic;
      }

      // Mettre en cache
      this.addToCache(cacheKey, result);

      console.log(`✅ Distance calculée: ${result.distance.text} (${result.duration.text})`);
      return result;
    } catch (error) {
      console.error('❌ Erreur calcul distance:', error);
      return null;
    }
  }

  /**
   * Calculer plusieurs distances en une seule requête
   */
  async calculateMultipleDistances(
    origins: (AddressDetails | { latitude: number; longitude: number } | string)[],
    destinations: (AddressDetails | { latitude: number; longitude: number } | string)[],
    options: DistanceMatrixOptions = {}
  ): Promise<DistanceCalculationResult[][]> {
    try {
      console.log('📏 Calcul distances multiples');

      const originsStr = origins.map(origin => this.formatLocation(origin)).filter(Boolean).join('|');
      const destinationsStr = destinations.map(dest => this.formatLocation(dest)).filter(Boolean).join('|');

      if (!originsStr || !destinationsStr) {
        throw new Error('Coordonnées invalides');
      }

      const params = new URLSearchParams({
        origins: originsStr,
        destinations: destinationsStr,
        key: this.apiKey,
        language: 'fr',
        units: options.units || 'metric',
        mode: options.mode || 'driving',
      });

      const response = await fetch(`${this.baseUrl}/json?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== 'OK') {
        throw new Error(`Distance Matrix API error: ${data.status}`);
      }

      const results: DistanceCalculationResult[][] = [];

      data.rows.forEach((row: any) => {
        const rowResults: DistanceCalculationResult[] = [];
        
        row.elements.forEach((element: any) => {
          const result: DistanceCalculationResult = {
            distance: element.distance || { text: 'N/A', value: 0 },
            duration: element.duration || { text: 'N/A', value: 0 },
            status: element.status,
          };

          if (element.duration_in_traffic) {
            result.durationInTraffic = element.duration_in_traffic;
          }

          rowResults.push(result);
        });

        results.push(rowResults);
      });

      console.log(`✅ ${results.length}x${results[0]?.length || 0} distances calculées`);
      return results;
    } catch (error) {
      console.error('❌ Erreur calcul distances multiples:', error);
      return [];
    }
  }

  /**
   * Obtenir l'itinéraire détaillé entre deux points
   */
  async getDetailedRoute(
    origin: AddressDetails | { latitude: number; longitude: number } | string,
    destination: AddressDetails | { latitude: number; longitude: number } | string,
    options: DistanceMatrixOptions = {}
  ): Promise<RouteInfo | null> {
    try {
      console.log('🗺️ Récupération itinéraire détaillé');

      const originStr = this.formatLocation(origin);
      const destinationStr = this.formatLocation(destination);

      if (!originStr || !destinationStr) {
        throw new Error('Coordonnées invalides');
      }

      const params = new URLSearchParams({
        origin: originStr,
        destination: destinationStr,
        key: this.apiKey,
        language: 'fr',
        mode: options.mode || 'driving',
      });

      // Options d'évitement
      const avoid = [];
      if (options.avoidTolls) avoid.push('tolls');
      if (options.avoidHighways) avoid.push('highways');
      if (options.avoidFerries) avoid.push('ferries');
      if (avoid.length > 0) {
        params.append('avoid', avoid.join('|'));
      }

      const response = await fetch(`${this.directionsUrl}/json?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== 'OK' || !data.routes || data.routes.length === 0) {
        throw new Error(`Directions API error: ${data.status}`);
      }

      const route = data.routes[0];
      const leg = route.legs[0];

      const routeInfo: RouteInfo = {
        origin: typeof origin === 'string' ? { latitude: 0, longitude: 0 } : origin,
        destination: typeof destination === 'string' ? { latitude: 0, longitude: 0 } : destination,
        distance: {
          distance: leg.distance,
          duration: leg.duration,
          status: 'OK',
        },
        routePolyline: route.overview_polyline?.points,
      };

      console.log('✅ Itinéraire détaillé récupéré');
      return routeInfo;
    } catch (error) {
      console.error('❌ Erreur itinéraire détaillé:', error);
      return null;
    }
  }

  /**
   * Calculer le coût de livraison basé sur la distance
   */
  calculateDeliveryCost(
    distance: DistanceCalculationResult,
    baseCost: number,
    costPerKm: number,
    minimumCost?: number
  ): number {
    const distanceKm = distance.distance.value / 1000;
    const calculatedCost = baseCost + (distanceKm * costPerKm);
    
    return minimumCost ? Math.max(calculatedCost, minimumCost) : calculatedCost;
  }

  /**
   * Estimer le temps de livraison avec marge
   */
  estimateDeliveryTime(
    distance: DistanceCalculationResult,
    preparationTime: number = 15, // minutes
    bufferPercentage: number = 20 // pourcentage de marge
  ): number {
    const travelTimeMinutes = distance.duration.value / 60;
    const totalTime = preparationTime + travelTimeMinutes;
    const withBuffer = totalTime * (1 + bufferPercentage / 100);
    
    return Math.ceil(withBuffer);
  }

  /**
   * Formater une localisation pour l'API
   */
  private formatLocation(location: AddressDetails | { latitude: number; longitude: number } | string): string | null {
    if (typeof location === 'string') {
      return encodeURIComponent(location);
    }

    if ('coordinates' in location) {
      return `${location.coordinates.latitude},${location.coordinates.longitude}`;
    }

    if ('latitude' in location && 'longitude' in location) {
      return `${location.latitude},${location.longitude}`;
    }

    return null;
  }

  /**
   * Créer une clé de cache
   */
  private createCacheKey(
    origin: any,
    destination: any,
    options: DistanceMatrixOptions
  ): string {
    const originStr = this.formatLocation(origin) || '';
    const destinationStr = this.formatLocation(destination) || '';
    const optionsStr = JSON.stringify(options);
    
    return `${originStr}-${destinationStr}-${optionsStr}`;
  }

  /**
   * Récupérer depuis le cache
   */
  private getFromCache(key: string): DistanceCalculationResult | null {
    const cached = this.cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.result;
    }
    
    if (cached) {
      this.cache.delete(key);
    }
    
    return null;
  }

  /**
   * Ajouter au cache
   */
  private addToCache(key: string, result: DistanceCalculationResult): void {
    this.cache.set(key, {
      result,
      timestamp: Date.now(),
    });

    // Nettoyer le cache si trop volumineux
    if (this.cache.size > 100) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Vider le cache
   */
  clearCache(): void {
    this.cache.clear();
    console.log('✅ Cache distance vidé');
  }

  /**
   * Obtenir les statistiques du cache
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }

  /**
   * Convertir les mètres en kilomètres avec formatage
   */
  formatDistance(meters: number): string {
    if (meters < 1000) {
      return `${Math.round(meters)} m`;
    } else {
      return `${(meters / 1000).toFixed(1)} km`;
    }
  }

  /**
   * Convertir les secondes en format lisible
   */
  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}min`;
    } else {
      return `${minutes} min`;
    }
  }
}

export const distanceCalculationService = new DistanceCalculationService();
export default distanceCalculationService;
