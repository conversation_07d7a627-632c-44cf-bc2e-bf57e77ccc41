import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

// Types
import { DeliveryTabParamList } from '../types';

// Écrans placeholders pour les livreurs
import DeliveryHomeScreen from '../screens/delivery/DeliveryHomeScreen';
import DeliveryOrdersScreen from '../screens/delivery/DeliveryOrdersScreen';
import DeliveryProfileScreen from '../screens/delivery/DeliveryProfileScreen';
import EditProfileScreen from '../screens/shared/EditProfileScreen';
import PreferencesScreen from '../screens/shared/PreferencesScreen';

const Tab = createBottomTabNavigator<DeliveryTabParamList>();
const Stack = createStackNavigator();

// Navigation par onglets pour les livreurs
const DeliveryTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'DeliveryHome':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'DeliveryOrders':
              iconName = focused ? 'list' : 'list-outline';
              break;
            case 'DeliveryProfile':
              iconName = focused ? 'person' : 'person-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: '#8E8E93',
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="DeliveryHome"
        component={DeliveryHomeScreen}
        options={{ title: 'Accueil' }}
      />
      <Tab.Screen
        name="DeliveryOrders"
        component={DeliveryOrdersScreen}
        options={{ title: 'Commandes' }}
      />
      <Tab.Screen
        name="DeliveryProfile"
        component={DeliveryProfileScreen}
        options={{ title: 'Profil' }}
      />
    </Tab.Navigator>
  );
};

// Navigation principale avec pile pour les détails
const DeliveryNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      {/* Navigation par onglets comme écran principal */}
      <Stack.Screen
        name="DeliveryTabs"
        component={DeliveryTabNavigator}
        options={{ headerShown: false }}
      />

      {/* Écran de modification du profil */}
      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{
          title: 'Modifier le profil',
          headerShown: false,
        }}
      />

      {/* Écran des préférences */}
      <Stack.Screen
        name="Preferences"
        component={PreferencesScreen}
        options={{
          title: 'Préférences',
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default DeliveryNavigator; 