// Types pour les services de livraison spécialisés

export interface SpecializedServiceCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  profitability: 'high' | 'medium' | 'low';
  estimatedMargin: number;
  services: SpecializedService[];
  specialRequirements: string[];
  partnerRequirements: string[];
}

export interface SpecializedService {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  pricing: SpecializedPricing;
  features: string[];
  requirements: string[];
  availability: ServiceAvailability;
  profitMetrics: ProfitMetrics;
  specialHandling: SpecialHandling;
  partnerIntegration: PartnerIntegration;
}

export interface SpecializedPricing {
  basePrice: number;
  currency: string;
  pricingModel: 'fixed' | 'weight' | 'size' | 'distance' | 'time' | 'value_percentage' | 'subscription';
  weightTiers?: WeightTier[];
  sizeTiers?: SizeTier[];
  distanceTiers?: DistanceTier[];
  subscriptionPlans?: SubscriptionPlan[];
  additionalFees: {
    expressDelivery?: number;
    fragileHandling?: number;
    insurance?: number;
    temperatureControl?: number;
    specialPackaging?: number;
    emergencyService?: number;
    pickupService?: number;
    customization?: number;
  };
  discounts?: {
    bulkOrders?: BulkDiscount[];
    loyaltyProgram?: LoyaltyDiscount[];
    firstTime?: number;
    subscription?: number;
  };
}

export interface WeightTier {
  minWeight: number; // en kg
  maxWeight: number;
  pricePerKg: number;
  basePrice: number;
}

export interface SizeTier {
  name: string;
  maxDimensions: {
    length: number; // en cm
    width: number;
    height: number;
  };
  price: number;
}

export interface DistanceTier {
  minDistance: number; // en km
  maxDistance: number;
  pricePerKm: number;
  basePrice: number;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  duration: number; // en jours
  deliveriesIncluded: number;
  monthlyPrice: number;
  perDeliveryPrice: number;
  benefits: string[];
}

export interface BulkDiscount {
  minQuantity: number;
  discountPercentage: number;
}

export interface LoyaltyDiscount {
  minOrders: number;
  discountPercentage: number;
}

export interface SpecialHandling {
  fragileItems: boolean;
  temperatureControl: boolean;
  secureTransport: boolean;
  specialPackaging: boolean;
  signatureRequired: boolean;
  photoProof: boolean;
  realTimeTracking: boolean;
  insuranceCoverage: boolean;
  customInstructions: boolean;
}

export interface PartnerIntegration {
  partnerType: 'pharmacy' | 'bakery' | 'electronics' | 'bookstore' | 'beauty_salon' | 'dry_cleaner' | 'restaurant' | 'general';
  verificationRequired: boolean;
  inventoryIntegration: boolean;
  appointmentBooking: boolean;
  customWorkflow: boolean;
  qualityStandards: string[];
  certificationRequired: string[];
}

export interface ServiceAvailability {
  cities: string[];
  timeSlots: {
    start: string;
    end: string;
    days: string[];
  }[];
  maxDeliveryTime: number; // en minutes
  minDeliveryTime: number;
  emergencyAvailable: boolean;
  subscriptionAvailable: boolean;
}

export interface ProfitMetrics {
  averageOrderValue: number;
  operationalCost: number;
  netMargin: number;
  volumeMultiplier: number;
  riskLevel: 'low' | 'medium' | 'high';
  seasonalVariation: number; // pourcentage de variation
}

// 1. COLIS & MERCHANDISE DELIVERY
export const COLIS_MERCHANDISE_SERVICES: SpecializedServiceCategory = {
  id: 'colis_merchandise',
  name: 'Colis & Marchandises',
  description: 'Livraison de colis et marchandises avec options spécialisées',
  icon: 'cube',
  color: '#3F51B5',
  profitability: 'high',
  estimatedMargin: 32,
  specialRequirements: [
    'Véhicules adaptés aux différentes tailles',
    'Matériel d\'emballage sécurisé',
    'Formation manipulation objets fragiles',
    'Système de traçabilité avancé'
  ],
  partnerRequirements: [
    'Assurance responsabilité civile',
    'Certification manipulation colis',
    'Équipement de protection'
  ],
  services: [
    {
      id: 'general_package',
      name: 'Livraison Colis Standard',
      description: 'Livraison de colis avec tarification basée sur poids et taille',
      icon: 'cube-outline',
      category: 'colis_merchandise',
      pricing: {
        basePrice: 1500,
        currency: 'XOF',
        pricingModel: 'weight',
        weightTiers: [
          { minWeight: 0, maxWeight: 2, pricePerKg: 750, basePrice: 1500 },
          { minWeight: 2, maxWeight: 5, pricePerKg: 600, basePrice: 2000 },
          { minWeight: 5, maxWeight: 10, pricePerKg: 500, basePrice: 3000 },
          { minWeight: 10, maxWeight: 25, pricePerKg: 400, basePrice: 5000 },
        ],
        sizeTiers: [
          { name: 'Petit', maxDimensions: { length: 30, width: 20, height: 15 }, price: 1500 },
          { name: 'Moyen', maxDimensions: { length: 50, width: 40, height: 30 }, price: 2500 },
          { name: 'Grand', maxDimensions: { length: 80, width: 60, height: 50 }, price: 4000 },
          { name: 'Très Grand', maxDimensions: { length: 120, width: 80, height: 70 }, price: 6500 },
        ],
        additionalFees: {
          expressDelivery: 1000,
          fragileHandling: 800,
          insurance: 500,
          specialPackaging: 600,
        },
        discounts: {
          bulkOrders: [
            { minQuantity: 5, discountPercentage: 10 },
            { minQuantity: 10, discountPercentage: 15 },
            { minQuantity: 20, discountPercentage: 20 },
          ],
          firstTime: 20,
        },
      },
      features: [
        'Tarification transparente poids/taille',
        'Suivi en temps réel',
        'Preuve de livraison photo',
        'Assurance optionnelle',
        'Support client dédié',
      ],
      requirements: [
        'Emballage approprié',
        'Étiquetage correct',
        'Déclaration de contenu',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké', 'San-Pédro', 'Yamoussoukro', 'Korhogo'],
        timeSlots: [
          { start: '08:00', end: '18:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi'] },
          { start: '09:00', end: '16:00', days: ['samedi'] },
        ],
        maxDeliveryTime: 240,
        minDeliveryTime: 60,
        emergencyAvailable: false,
        subscriptionAvailable: true,
      },
      profitMetrics: {
        averageOrderValue: 3500,
        operationalCost: 2000,
        netMargin: 1500,
        volumeMultiplier: 1.3,
        riskLevel: 'low',
        seasonalVariation: 15,
      },
      specialHandling: {
        fragileItems: true,
        temperatureControl: false,
        secureTransport: true,
        specialPackaging: true,
        signatureRequired: true,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: true,
        customInstructions: true,
      },
      partnerIntegration: {
        partnerType: 'general',
        verificationRequired: false,
        inventoryIntegration: false,
        appointmentBooking: false,
        customWorkflow: false,
        qualityStandards: ['Emballage sécurisé', 'Manipulation soigneuse'],
        certificationRequired: [],
      },
    },
    {
      id: 'fragile_items',
      name: 'Objets Fragiles Premium',
      description: 'Livraison spécialisée pour objets fragiles avec emballage renforcé',
      icon: 'shield-checkmark',
      category: 'colis_merchandise',
      pricing: {
        basePrice: 3000,
        currency: 'XOF',
        pricingModel: 'value_percentage',
        additionalFees: {
          fragileHandling: 1500,
          specialPackaging: 1200,
          insurance: 800,
          expressDelivery: 1500,
        },
      },
      features: [
        'Emballage renforcé professionnel',
        'Manipulation ultra-soigneuse',
        'Assurance tous risques incluse',
        'Livreur spécialisé formé',
        'Suivi GPS en continu',
      ],
      requirements: [
        'Déclaration de valeur obligatoire',
        'Photos avant transport',
        'Emballage d\'origine recommandé',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké'],
        timeSlots: [
          { start: '09:00', end: '17:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi'] },
        ],
        maxDeliveryTime: 180,
        minDeliveryTime: 90,
        emergencyAvailable: false,
        subscriptionAvailable: false,
      },
      profitMetrics: {
        averageOrderValue: 8500,
        operationalCost: 4500,
        netMargin: 4000,
        volumeMultiplier: 1.1,
        riskLevel: 'medium',
        seasonalVariation: 10,
      },
      specialHandling: {
        fragileItems: true,
        temperatureControl: false,
        secureTransport: true,
        specialPackaging: true,
        signatureRequired: true,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: true,
        customInstructions: true,
      },
      partnerIntegration: {
        partnerType: 'general',
        verificationRequired: true,
        inventoryIntegration: false,
        appointmentBooking: false,
        customWorkflow: true,
        qualityStandards: ['Emballage professionnel', 'Manipulation experte', 'Assurance complète'],
        certificationRequired: ['Formation objets fragiles'],
      },
    },
    {
      id: 'express_delivery',
      name: 'Livraison Express',
      description: 'Livraison ultra-rapide en moins de 2 heures',
      icon: 'flash',
      category: 'colis_merchandise',
      pricing: {
        basePrice: 2500,
        currency: 'XOF',
        pricingModel: 'fixed',
        distanceTiers: [
          { minDistance: 0, maxDistance: 5, pricePerKm: 0, basePrice: 2500 },
          { minDistance: 5, maxDistance: 15, pricePerKm: 200, basePrice: 3500 },
          { minDistance: 15, maxDistance: 30, pricePerKm: 300, basePrice: 5000 },
        ],
        additionalFees: {
          emergencyService: 1500,
          insurance: 500,
        },
      },
      features: [
        'Livraison en moins de 2h',
        'Priorité absolue',
        'Suivi en temps réel',
        'Notification instantanée',
        'Service 7j/7',
      ],
      requirements: [
        'Disponibilité du destinataire',
        'Adresse précise',
        'Téléphone joignable',
      ],
      availability: {
        cities: ['Abidjan'],
        timeSlots: [
          { start: '07:00', end: '20:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi', 'dimanche'] },
        ],
        maxDeliveryTime: 120,
        minDeliveryTime: 30,
        emergencyAvailable: true,
        subscriptionAvailable: false,
      },
      profitMetrics: {
        averageOrderValue: 4500,
        operationalCost: 2500,
        netMargin: 2000,
        volumeMultiplier: 1.2,
        riskLevel: 'medium',
        seasonalVariation: 25,
      },
      specialHandling: {
        fragileItems: false,
        temperatureControl: false,
        secureTransport: true,
        specialPackaging: false,
        signatureRequired: true,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: true,
        customInstructions: false,
      },
      partnerIntegration: {
        partnerType: 'general',
        verificationRequired: false,
        inventoryIntegration: false,
        appointmentBooking: false,
        customWorkflow: false,
        qualityStandards: ['Rapidité', 'Fiabilité'],
        certificationRequired: [],
      },
    },
  ],
};

// 2. PHARMACY & HEALTHCARE SERVICES
export const PHARMACY_HEALTHCARE_SERVICES: SpecializedServiceCategory = {
  id: 'pharmacy_healthcare',
  name: 'Pharmacie & Santé',
  description: 'Livraison de médicaments et produits de santé avec vérification',
  icon: 'medical',
  color: '#4CAF50',
  profitability: 'high',
  estimatedMargin: 35,
  specialRequirements: [
    'Licence de transport pharmaceutique',
    'Chaîne du froid pour certains médicaments',
    'Formation réglementation sanitaire',
    'Vérification ordonnances'
  ],
  partnerRequirements: [
    'Pharmacie agréée',
    'Pharmacien diplômé',
    'Autorisation de vente',
    'Assurance professionnelle'
  ],
  services: [
    {
      id: 'prescription_delivery',
      name: 'Livraison sur Ordonnance',
      description: 'Médicaments sur prescription avec vérification pharmaceutique',
      icon: 'document-text',
      category: 'pharmacy_healthcare',
      pricing: {
        basePrice: 1200,
        currency: 'XOF',
        pricingModel: 'fixed',
        additionalFees: {
          emergencyService: 2000,
          temperatureControl: 800,
          expressDelivery: 1000,
        },
        discounts: {
          loyaltyProgram: [
            { minOrders: 5, discountPercentage: 10 },
            { minOrders: 15, discountPercentage: 15 },
          ],
        },
      },
      features: [
        'Vérification ordonnance par pharmacien',
        'Conseil pharmaceutique inclus',
        'Respect chaîne du froid',
        'Livraison sécurisée',
        'Historique médical privé',
      ],
      requirements: [
        'Ordonnance valide obligatoire',
        'Pièce d\'identité du patient',
        'Vérification âge si nécessaire',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké', 'San-Pédro'],
        timeSlots: [
          { start: '08:00', end: '20:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi'] },
          { start: '09:00', end: '18:00', days: ['samedi'] },
          { start: '10:00', end: '16:00', days: ['dimanche'] },
        ],
        maxDeliveryTime: 90,
        minDeliveryTime: 30,
        emergencyAvailable: true,
        subscriptionAvailable: true,
      },
      profitMetrics: {
        averageOrderValue: 8500,
        operationalCost: 4000,
        netMargin: 4500,
        volumeMultiplier: 1.4,
        riskLevel: 'medium',
        seasonalVariation: 20,
      },
      specialHandling: {
        fragileItems: true,
        temperatureControl: true,
        secureTransport: true,
        specialPackaging: true,
        signatureRequired: true,
        photoProof: false,
        realTimeTracking: true,
        insuranceCoverage: true,
        customInstructions: true,
      },
      partnerIntegration: {
        partnerType: 'pharmacy',
        verificationRequired: true,
        inventoryIntegration: true,
        appointmentBooking: false,
        customWorkflow: true,
        qualityStandards: ['Respect chaîne du froid', 'Vérification ordonnance', 'Conseil pharmaceutique'],
        certificationRequired: ['Licence pharmaceutique', 'Formation transport médicaments'],
      },
    },
    {
      id: 'otc_medicines',
      name: 'Médicaments Sans Ordonnance',
      description: 'Produits de santé et médicaments en vente libre',
      icon: 'fitness',
      category: 'pharmacy_healthcare',
      pricing: {
        basePrice: 800,
        currency: 'XOF',
        pricingModel: 'fixed',
        additionalFees: {
          expressDelivery: 600,
          bulkOrder: -200, // Réduction pour commandes groupées
        },
        subscriptionPlans: [
          {
            id: 'health_basic',
            name: 'Santé Basique',
            duration: 30,
            deliveriesIncluded: 4,
            monthlyPrice: 2500,
            perDeliveryPrice: 500,
            benefits: ['Livraison gratuite', 'Conseil santé', 'Rappels médicaments'],
          },
        ],
      },
      features: [
        'Large gamme de produits OTC',
        'Conseil santé gratuit',
        'Livraison rapide',
        'Produits authentiques garantis',
        'Programme de fidélité',
      ],
      requirements: [
        'Vérification âge pour certains produits',
        'Respect posologie recommandée',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké', 'San-Pédro', 'Yamoussoukro'],
        timeSlots: [
          { start: '08:00', end: '21:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'] },
          { start: '10:00', end: '18:00', days: ['dimanche'] },
        ],
        maxDeliveryTime: 60,
        minDeliveryTime: 20,
        emergencyAvailable: false,
        subscriptionAvailable: true,
      },
      profitMetrics: {
        averageOrderValue: 4500,
        operationalCost: 2200,
        netMargin: 2300,
        volumeMultiplier: 1.6,
        riskLevel: 'low',
        seasonalVariation: 30,
      },
      specialHandling: {
        fragileItems: false,
        temperatureControl: false,
        secureTransport: false,
        specialPackaging: false,
        signatureRequired: false,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: false,
        customInstructions: false,
      },
      partnerIntegration: {
        partnerType: 'pharmacy',
        verificationRequired: true,
        inventoryIntegration: true,
        appointmentBooking: false,
        customWorkflow: false,
        qualityStandards: ['Produits authentiques', 'Conseil approprié'],
        certificationRequired: ['Autorisation vente OTC'],
      },
    },
    {
      id: 'emergency_medication',
      name: 'Médicaments d\'Urgence',
      description: 'Livraison d\'urgence de médicaments en moins de 30 minutes',
      icon: 'alarm',
      category: 'pharmacy_healthcare',
      pricing: {
        basePrice: 3000,
        currency: 'XOF',
        pricingModel: 'fixed',
        additionalFees: {
          emergencyService: 2500,
          nightService: 1500,
        },
      },
      features: [
        'Livraison en moins de 30 minutes',
        'Service 24h/24 disponible',
        'Pharmacien de garde',
        'Priorité absolue',
        'Suivi GPS en temps réel',
      ],
      requirements: [
        'Urgence médicale justifiée',
        'Ordonnance ou prescription valide',
        'Contact médecin traitant',
      ],
      availability: {
        cities: ['Abidjan'],
        timeSlots: [
          { start: '00:00', end: '23:59', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi', 'dimanche'] },
        ],
        maxDeliveryTime: 30,
        minDeliveryTime: 15,
        emergencyAvailable: true,
        subscriptionAvailable: false,
      },
      profitMetrics: {
        averageOrderValue: 12000,
        operationalCost: 6500,
        netMargin: 5500,
        volumeMultiplier: 1.1,
        riskLevel: 'high',
        seasonalVariation: 15,
      },
      specialHandling: {
        fragileItems: true,
        temperatureControl: true,
        secureTransport: true,
        specialPackaging: true,
        signatureRequired: true,
        photoProof: false,
        realTimeTracking: true,
        insuranceCoverage: true,
        customInstructions: true,
      },
      partnerIntegration: {
        partnerType: 'pharmacy',
        verificationRequired: true,
        inventoryIntegration: true,
        appointmentBooking: false,
        customWorkflow: true,
        qualityStandards: ['Rapidité extrême', 'Disponibilité 24h/24', 'Expertise urgence'],
        certificationRequired: ['Pharmacien de garde', 'Formation urgences'],
      },
    },
  ],
};

// 3. BAKERY & FRESH FOOD SERVICES
export const BAKERY_FRESH_FOOD_SERVICES: SpecializedServiceCategory = {
  id: 'bakery_fresh_food',
  name: 'Boulangerie & Produits Frais',
  description: 'Livraison de pain frais, pâtisseries et produits périssables',
  icon: 'cafe',
  color: '#FF9800',
  profitability: 'medium',
  estimatedMargin: 28,
  specialRequirements: [
    'Véhicules avec contrôle température',
    'Emballage alimentaire certifié',
    'Respect chaîne du froid',
    'Formation hygiène alimentaire'
  ],
  partnerRequirements: [
    'Licence boulangerie/pâtisserie',
    'Certification hygiène HACCP',
    'Assurance alimentaire',
    'Équipement professionnel'
  ],
  services: [
    {
      id: 'fresh_bread_delivery',
      name: 'Pain Frais Quotidien',
      description: 'Livraison de pain frais et viennoiseries du jour',
      icon: 'restaurant',
      category: 'bakery_fresh_food',
      pricing: {
        basePrice: 500,
        currency: 'XOF',
        pricingModel: 'fixed',
        subscriptionPlans: [
          {
            id: 'daily_bread',
            name: 'Pain Quotidien',
            duration: 30,
            deliveriesIncluded: 30,
            monthlyPrice: 12000,
            perDeliveryPrice: 400,
            benefits: ['Livraison quotidienne', 'Pain toujours frais', 'Priorité commandes'],
          },
          {
            id: 'weekly_bread',
            name: 'Pain Hebdomadaire',
            duration: 30,
            deliveriesIncluded: 8,
            monthlyPrice: 3500,
            perDeliveryPrice: 450,
            benefits: ['Livraison 2x/semaine', 'Variété de pains', 'Flexibilité horaires'],
          },
        ],
        additionalFees: {
          expressDelivery: 300,
          temperatureControl: 200,
        },
        discounts: {
          subscription: 20,
          bulkOrders: [
            { minQuantity: 5, discountPercentage: 10 },
          ],
        },
      },
      features: [
        'Pain cuit le jour même',
        'Livraison matinale possible',
        'Variété de pains disponibles',
        'Emballage hygiénique',
        'Abonnement flexible',
      ],
      requirements: [
        'Commande avant 20h pour le lendemain',
        'Adresse accessible le matin',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké', 'San-Pédro'],
        timeSlots: [
          { start: '06:00', end: '10:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'] },
          { start: '07:00', end: '11:00', days: ['dimanche'] },
        ],
        maxDeliveryTime: 60,
        minDeliveryTime: 30,
        emergencyAvailable: false,
        subscriptionAvailable: true,
      },
      profitMetrics: {
        averageOrderValue: 2500,
        operationalCost: 1500,
        netMargin: 1000,
        volumeMultiplier: 2.5,
        riskLevel: 'low',
        seasonalVariation: 10,
      },
      specialHandling: {
        fragileItems: false,
        temperatureControl: true,
        secureTransport: false,
        specialPackaging: true,
        signatureRequired: false,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: false,
        customInstructions: true,
      },
      partnerIntegration: {
        partnerType: 'bakery',
        verificationRequired: true,
        inventoryIntegration: true,
        appointmentBooking: false,
        customWorkflow: true,
        qualityStandards: ['Fraîcheur garantie', 'Hygiène stricte', 'Ponctualité'],
        certificationRequired: ['HACCP', 'Licence boulangerie'],
      },
    },
    {
      id: 'custom_cakes',
      name: 'Gâteaux Personnalisés',
      description: 'Commande et livraison de gâteaux sur mesure pour occasions spéciales',
      icon: 'gift',
      category: 'bakery_fresh_food',
      pricing: {
        basePrice: 2000,
        currency: 'XOF',
        pricingModel: 'value_percentage',
        additionalFees: {
          customization: 1500,
          expressDelivery: 1000,
          specialPackaging: 800,
          temperatureControl: 600,
        },
      },
      features: [
        'Design personnalisé',
        'Choix des saveurs',
        'Décoration sur mesure',
        'Livraison soignée',
        'Photos avant livraison',
      ],
      requirements: [
        'Commande 48h à l\'avance minimum',
        'Acompte de 50% requis',
        'Validation design avant production',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké'],
        timeSlots: [
          { start: '09:00', end: '18:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'] },
          { start: '10:00', end: '16:00', days: ['dimanche'] },
        ],
        maxDeliveryTime: 120,
        minDeliveryTime: 60,
        emergencyAvailable: false,
        subscriptionAvailable: false,
      },
      profitMetrics: {
        averageOrderValue: 15000,
        operationalCost: 8000,
        netMargin: 7000,
        volumeMultiplier: 1.2,
        riskLevel: 'medium',
        seasonalVariation: 40,
      },
      specialHandling: {
        fragileItems: true,
        temperatureControl: true,
        secureTransport: true,
        specialPackaging: true,
        signatureRequired: true,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: true,
        customInstructions: true,
      },
      partnerIntegration: {
        partnerType: 'bakery',
        verificationRequired: true,
        inventoryIntegration: false,
        appointmentBooking: true,
        customWorkflow: true,
        qualityStandards: ['Qualité artistique', 'Fraîcheur parfaite', 'Respect délais'],
        certificationRequired: ['Pâtissier qualifié', 'Atelier agréé'],
      },
    },
    {
      id: 'perishable_delivery',
      name: 'Produits Périssables',
      description: 'Livraison avec contrôle température pour produits frais',
      icon: 'snow',
      category: 'bakery_fresh_food',
      pricing: {
        basePrice: 1200,
        currency: 'XOF',
        pricingModel: 'weight',
        weightTiers: [
          { minWeight: 0, maxWeight: 1, pricePerKg: 1200, basePrice: 1200 },
          { minWeight: 1, maxWeight: 3, pricePerKg: 800, basePrice: 1500 },
          { minWeight: 3, maxWeight: 5, pricePerKg: 600, basePrice: 2000 },
        ],
        additionalFees: {
          temperatureControl: 800,
          expressDelivery: 800,
          specialPackaging: 400,
        },
      },
      features: [
        'Chaîne du froid respectée',
        'Emballage isotherme',
        'Livraison rapide',
        'Contrôle température',
        'Garantie fraîcheur',
      ],
      requirements: [
        'Réception immédiate obligatoire',
        'Stockage approprié chez destinataire',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké'],
        timeSlots: [
          { start: '08:00', end: '16:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'] },
        ],
        maxDeliveryTime: 90,
        minDeliveryTime: 30,
        emergencyAvailable: false,
        subscriptionAvailable: true,
      },
      profitMetrics: {
        averageOrderValue: 4500,
        operationalCost: 2800,
        netMargin: 1700,
        volumeMultiplier: 1.3,
        riskLevel: 'medium',
        seasonalVariation: 35,
      },
      specialHandling: {
        fragileItems: true,
        temperatureControl: true,
        secureTransport: false,
        specialPackaging: true,
        signatureRequired: true,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: false,
        customInstructions: true,
      },
      partnerIntegration: {
        partnerType: 'bakery',
        verificationRequired: true,
        inventoryIntegration: true,
        appointmentBooking: false,
        customWorkflow: true,
        qualityStandards: ['Chaîne du froid', 'Rapidité livraison', 'Emballage adapté'],
        certificationRequired: ['Transport frigorifique'],
      },
    },
  ],
};

// 4. DRY CLEANING & LAUNDRY SERVICES
export const DRY_CLEANING_LAUNDRY_SERVICES: SpecializedServiceCategory = {
  id: 'dry_cleaning_laundry',
  name: 'Pressing & Nettoyage',
  description: 'Services de nettoyage à sec et pressing avec collecte/livraison',
  icon: 'shirt',
  color: '#9C27B0',
  profitability: 'medium',
  estimatedMargin: 30,
  specialRequirements: [
    'Partenariat avec pressings agréés',
    'Système de traçabilité des vêtements',
    'Emballage protecteur spécialisé',
    'Formation manipulation textiles délicats'
  ],
  partnerRequirements: [
    'Licence pressing/nettoyage',
    'Équipement professionnel',
    'Assurance dommages textiles',
    'Certification qualité'
  ],
  services: [
    {
      id: 'standard_cleaning',
      name: 'Nettoyage Standard',
      description: 'Collecte et livraison pour nettoyage à sec standard',
      icon: 'shirt-outline',
      category: 'dry_cleaning_laundry',
      pricing: {
        basePrice: 1500,
        currency: 'XOF',
        pricingModel: 'fixed',
        subscriptionPlans: [
          {
            id: 'weekly_cleaning',
            name: 'Nettoyage Hebdomadaire',
            duration: 30,
            deliveriesIncluded: 4,
            monthlyPrice: 5000,
            perDeliveryPrice: 1200,
            benefits: ['Collecte/livraison incluse', 'Priorité traitement', 'Remise fidélité'],
          },
        ],
        additionalFees: {
          expressDelivery: 1000,
          specialPackaging: 300,
          pickupService: 500,
        },
        discounts: {
          subscription: 20,
          bulkOrders: [
            { minQuantity: 5, discountPercentage: 15 },
          ],
        },
      },
      features: [
        'Collecte à domicile',
        'Nettoyage professionnel',
        'Livraison soignée',
        'Traçabilité complète',
        'Garantie qualité',
      ],
      requirements: [
        'Vêtements triés par type',
        'Signalement taches spéciales',
        'Disponibilité pour collecte',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké'],
        timeSlots: [
          { start: '08:00', end: '17:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi'] },
          { start: '09:00', end: '15:00', days: ['samedi'] },
        ],
        maxDeliveryTime: 2880, // 48h
        minDeliveryTime: 1440, // 24h
        emergencyAvailable: false,
        subscriptionAvailable: true,
      },
      profitMetrics: {
        averageOrderValue: 6500,
        operationalCost: 3500,
        netMargin: 3000,
        volumeMultiplier: 1.4,
        riskLevel: 'medium',
        seasonalVariation: 20,
      },
      specialHandling: {
        fragileItems: true,
        temperatureControl: false,
        secureTransport: true,
        specialPackaging: true,
        signatureRequired: true,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: true,
        customInstructions: true,
      },
      partnerIntegration: {
        partnerType: 'dry_cleaner',
        verificationRequired: true,
        inventoryIntegration: false,
        appointmentBooking: true,
        customWorkflow: true,
        qualityStandards: ['Nettoyage professionnel', 'Respect délais', 'Soin des textiles'],
        certificationRequired: ['Licence pressing', 'Formation textiles'],
      },
    },
  ],
};

// 5. ELECTRONICS & TECHNOLOGY SERVICES
export const ELECTRONICS_TECHNOLOGY_SERVICES: SpecializedServiceCategory = {
  id: 'electronics_technology',
  name: 'Électronique & Technologie',
  description: 'Livraison d\'appareils électroniques avec assurance et support',
  icon: 'phone-portrait',
  color: '#607D8B',
  profitability: 'high',
  estimatedMargin: 25,
  specialRequirements: [
    'Emballage anti-statique',
    'Assurance tous risques obligatoire',
    'Formation manipulation électronique',
    'Véhicules sécurisés'
  ],
  partnerRequirements: [
    'Revendeur agréé',
    'Garantie constructeur',
    'Service après-vente',
    'Certification qualité'
  ],
  services: [
    {
      id: 'mobile_electronics',
      name: 'Téléphones & Accessoires',
      description: 'Livraison de smartphones, tablettes et accessoires',
      icon: 'phone-portrait-outline',
      category: 'electronics_technology',
      pricing: {
        basePrice: 2000,
        currency: 'XOF',
        pricingModel: 'value_percentage',
        additionalFees: {
          insurance: 1500,
          expressDelivery: 1500,
          specialPackaging: 800,
        },
      },
      features: [
        'Assurance transport incluse',
        'Emballage sécurisé',
        'Vérification avant livraison',
        'Garantie constructeur',
        'Support technique',
      ],
      requirements: [
        'Vérification identité obligatoire',
        'Signature à la livraison',
        'Test fonctionnel accepté',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké', 'San-Pédro'],
        timeSlots: [
          { start: '09:00', end: '18:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi'] },
          { start: '10:00', end: '16:00', days: ['samedi'] },
        ],
        maxDeliveryTime: 180,
        minDeliveryTime: 60,
        emergencyAvailable: true,
        subscriptionAvailable: false,
      },
      profitMetrics: {
        averageOrderValue: 85000,
        operationalCost: 55000,
        netMargin: 30000,
        volumeMultiplier: 1.1,
        riskLevel: 'high',
        seasonalVariation: 30,
      },
      specialHandling: {
        fragileItems: true,
        temperatureControl: false,
        secureTransport: true,
        specialPackaging: true,
        signatureRequired: true,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: true,
        customInstructions: false,
      },
      partnerIntegration: {
        partnerType: 'electronics',
        verificationRequired: true,
        inventoryIntegration: true,
        appointmentBooking: false,
        customWorkflow: true,
        qualityStandards: ['Produits authentiques', 'État neuf garanti', 'Garantie valide'],
        certificationRequired: ['Revendeur agréé', 'Certification produits'],
      },
    },
  ],
};

// 6. BOOKSTORE & STATIONERY SERVICES
export const BOOKSTORE_STATIONERY_SERVICES: SpecializedServiceCategory = {
  id: 'bookstore_stationery',
  name: 'Librairie & Papeterie',
  description: 'Livraison de livres, fournitures scolaires et services d\'impression',
  icon: 'library',
  color: '#795548',
  profitability: 'medium',
  estimatedMargin: 25,
  specialRequirements: [
    'Emballage protecteur pour livres',
    'Partenariat avec librairies',
    'Service d\'impression sur demande',
    'Gestion stock scolaire saisonnier'
  ],
  partnerRequirements: [
    'Licence librairie',
    'Stock diversifié',
    'Équipement impression',
    'Partenariat éditeurs'
  ],
  services: [
    {
      id: 'books_delivery',
      name: 'Livraison de Livres',
      description: 'Livres, manuels scolaires et matériel éducatif',
      icon: 'book',
      category: 'bookstore_stationery',
      pricing: {
        basePrice: 800,
        currency: 'XOF',
        pricingModel: 'fixed',
        additionalFees: {
          expressDelivery: 600,
          specialPackaging: 300,
        },
        discounts: {
          bulkOrders: [
            { minQuantity: 5, discountPercentage: 10 },
            { minQuantity: 10, discountPercentage: 15 },
          ],
        },
      },
      features: [
        'Large catalogue disponible',
        'Livres neufs et d\'occasion',
        'Emballage protecteur',
        'Commande sur catalogue',
        'Livraison soignée',
      ],
      requirements: [
        'Commande minimum 2000 XOF',
        'Délai 24h pour livres spéciaux',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké', 'Yamoussoukro'],
        timeSlots: [
          { start: '09:00', end: '18:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi'] },
          { start: '10:00', end: '16:00', days: ['samedi'] },
        ],
        maxDeliveryTime: 120,
        minDeliveryTime: 60,
        emergencyAvailable: false,
        subscriptionAvailable: true,
      },
      profitMetrics: {
        averageOrderValue: 12000,
        operationalCost: 7500,
        netMargin: 4500,
        volumeMultiplier: 1.3,
        riskLevel: 'low',
        seasonalVariation: 60, // Forte variation rentrée scolaire
      },
      specialHandling: {
        fragileItems: false,
        temperatureControl: false,
        secureTransport: false,
        specialPackaging: true,
        signatureRequired: false,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: false,
        customInstructions: false,
      },
      partnerIntegration: {
        partnerType: 'bookstore',
        verificationRequired: true,
        inventoryIntegration: true,
        appointmentBooking: false,
        customWorkflow: false,
        qualityStandards: ['Livres en bon état', 'Emballage soigné', 'Catalogue à jour'],
        certificationRequired: ['Licence librairie'],
      },
    },
  ],
};

// 7. BEAUTY & HAIR SALON SERVICES
export const BEAUTY_HAIR_SALON_SERVICES: SpecializedServiceCategory = {
  id: 'beauty_hair_salon',
  name: 'Beauté & Coiffure',
  description: 'Services de beauté à domicile et livraison de produits',
  icon: 'cut',
  color: '#E91E63',
  profitability: 'high',
  estimatedMargin: 40,
  specialRequirements: [
    'Coiffeurs/esthéticiennes mobiles',
    'Équipement portable professionnel',
    'Produits de qualité certifiés',
    'Hygiène et sécurité strictes'
  ],
  partnerRequirements: [
    'Diplôme coiffure/esthétique',
    'Assurance professionnelle',
    'Équipement mobile',
    'Certification hygiène'
  ],
  services: [
    {
      id: 'mobile_hairdressing',
      name: 'Coiffure à Domicile',
      description: 'Service de coiffure professionnel à domicile',
      icon: 'cut-outline',
      category: 'beauty_hair_salon',
      pricing: {
        basePrice: 8000,
        currency: 'XOF',
        pricingModel: 'fixed',
        additionalFees: {
          customization: 3000,
          specialPackaging: 500,
        },
        subscriptionPlans: [
          {
            id: 'beauty_monthly',
            name: 'Beauté Mensuelle',
            duration: 30,
            deliveriesIncluded: 2,
            monthlyPrice: 14000,
            perDeliveryPrice: 7000,
            benefits: ['Coiffure + soin', 'Produits inclus', 'Priorité réservation'],
          },
        ],
      },
      features: [
        'Coiffeur professionnel à domicile',
        'Équipement portable inclus',
        'Produits de qualité',
        'Service personnalisé',
        'Hygiène garantie',
      ],
      requirements: [
        'Espace approprié disponible',
        'Point d\'eau accessible',
        'Rendez-vous confirmé 24h avant',
      ],
      availability: {
        cities: ['Abidjan'],
        timeSlots: [
          { start: '09:00', end: '18:00', days: ['mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'] },
          { start: '10:00', end: '16:00', days: ['dimanche'] },
        ],
        maxDeliveryTime: 180,
        minDeliveryTime: 120,
        emergencyAvailable: false,
        subscriptionAvailable: true,
      },
      profitMetrics: {
        averageOrderValue: 15000,
        operationalCost: 7000,
        netMargin: 8000,
        volumeMultiplier: 1.2,
        riskLevel: 'medium',
        seasonalVariation: 25,
      },
      specialHandling: {
        fragileItems: false,
        temperatureControl: false,
        secureTransport: false,
        specialPackaging: false,
        signatureRequired: true,
        photoProof: false,
        realTimeTracking: true,
        insuranceCoverage: true,
        customInstructions: true,
      },
      partnerIntegration: {
        partnerType: 'beauty_salon',
        verificationRequired: true,
        inventoryIntegration: false,
        appointmentBooking: true,
        customWorkflow: true,
        qualityStandards: ['Service professionnel', 'Hygiène stricte', 'Satisfaction client'],
        certificationRequired: ['Diplôme coiffure', 'Certification hygiène'],
      },
    },
  ],
};

// 8. ENHANCED RESTAURANT & FOOD DELIVERY
export const ENHANCED_RESTAURANT_FOOD_SERVICES: SpecializedServiceCategory = {
  id: 'enhanced_restaurant_food',
  name: 'Restaurant & Traiteur Premium',
  description: 'Livraison restaurant améliorée avec garanties et services traiteur',
  icon: 'restaurant',
  color: '#FF5722',
  profitability: 'high',
  estimatedMargin: 30,
  specialRequirements: [
    'Sacs isothermes professionnels',
    'Contrôle température en continu',
    'Partenariat restaurants premium',
    'Service traiteur événements'
  ],
  partnerRequirements: [
    'Restaurant agréé',
    'Certification hygiène HACCP',
    'Équipement professionnel',
    'Assurance alimentaire'
  ],
  services: [
    {
      id: 'hot_food_guarantee',
      name: 'Garantie Plat Chaud',
      description: 'Livraison restaurant avec garantie température et fraîcheur',
      icon: 'flame',
      category: 'enhanced_restaurant_food',
      pricing: {
        basePrice: 1200,
        currency: 'XOF',
        pricingModel: 'fixed',
        additionalFees: {
          temperatureControl: 500,
          expressDelivery: 800,
        },
        discounts: {
          loyaltyProgram: [
            { minOrders: 10, discountPercentage: 10 },
            { minOrders: 25, discountPercentage: 15 },
          ],
        },
      },
      features: [
        'Garantie plat chaud à l\'arrivée',
        'Sacs isothermes professionnels',
        'Contrôle température',
        'Remboursement si froid',
        'Livraison en moins de 45 min',
      ],
      requirements: [
        'Réception immédiate obligatoire',
        'Vérification température acceptée',
      ],
      availability: {
        cities: ['Abidjan', 'Bouaké'],
        timeSlots: [
          { start: '11:00', end: '14:30', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi', 'dimanche'] },
          { start: '18:00', end: '22:00', days: ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi', 'dimanche'] },
        ],
        maxDeliveryTime: 45,
        minDeliveryTime: 20,
        emergencyAvailable: false,
        subscriptionAvailable: true,
      },
      profitMetrics: {
        averageOrderValue: 6500,
        operationalCost: 3500,
        netMargin: 3000,
        volumeMultiplier: 2.1,
        riskLevel: 'medium',
        seasonalVariation: 15,
      },
      specialHandling: {
        fragileItems: false,
        temperatureControl: true,
        secureTransport: false,
        specialPackaging: true,
        signatureRequired: false,
        photoProof: true,
        realTimeTracking: true,
        insuranceCoverage: false,
        customInstructions: true,
      },
      partnerIntegration: {
        partnerType: 'restaurant',
        verificationRequired: true,
        inventoryIntegration: true,
        appointmentBooking: false,
        customWorkflow: true,
        qualityStandards: ['Température maintenue', 'Fraîcheur garantie', 'Emballage adapté'],
        certificationRequired: ['HACCP', 'Licence restaurant'],
      },
    },
  ],
};

// Export des catégories spécialisées
export const SPECIALIZED_SERVICE_CATEGORIES = [
  COLIS_MERCHANDISE_SERVICES,
  PHARMACY_HEALTHCARE_SERVICES,
  BAKERY_FRESH_FOOD_SERVICES,
  DRY_CLEANING_LAUNDRY_SERVICES,
  ELECTRONICS_TECHNOLOGY_SERVICES,
  BOOKSTORE_STATIONERY_SERVICES,
  BEAUTY_HAIR_SALON_SERVICES,
  ENHANCED_RESTAURANT_FOOD_SERVICES,
];

export default SPECIALIZED_SERVICE_CATEGORIES;
