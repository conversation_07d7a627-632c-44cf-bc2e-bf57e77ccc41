// Service de gestion des services spécialisés
import { supabase } from './supabase';
import { SpecializedService, SpecializedServiceCategory } from '../types/specializedServices';

export interface ServiceOrder {
  id: string;
  userId: string;
  serviceId: string;
  categoryId: string;
  orderDetails: {
    items: OrderItem[];
    specialInstructions?: string;
    pickupAddress?: string;
    deliveryAddress: string;
    scheduledTime?: string;
    urgentDelivery: boolean;
  };
  pricing: {
    basePrice: number;
    additionalFees: Record<string, number>;
    totalPrice: number;
    currency: string;
  };
  status: 'pending' | 'confirmed' | 'pickup_scheduled' | 'picked_up' | 'in_process' | 'ready' | 'out_for_delivery' | 'delivered' | 'cancelled';
  partnerInfo?: {
    partnerId: string;
    partnerName: string;
    contactInfo: string;
  };
  trackingInfo: {
    currentLocation?: { latitude: number; longitude: number };
    estimatedDeliveryTime?: string;
    deliveryPersonId?: string;
    statusHistory: StatusUpdate[];
  };
  paymentInfo: {
    method: 'cash' | 'mobile_money' | 'card';
    status: 'pending' | 'paid' | 'refunded';
    transactionId?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  specialRequirements?: string[];
  customization?: Record<string, any>;
}

export interface StatusUpdate {
  status: string;
  timestamp: string;
  location?: { latitude: number; longitude: number };
  notes?: string;
  updatedBy: string;
}

export interface ServicePartner {
  id: string;
  name: string;
  category: string;
  serviceTypes: string[];
  contactInfo: {
    phone: string;
    email: string;
    address: string;
  };
  businessInfo: {
    license: string;
    certifications: string[];
    operatingHours: {
      [day: string]: { open: string; close: string };
    };
    serviceAreas: string[];
  };
  ratings: {
    averageRating: number;
    totalReviews: number;
    qualityScore: number;
    timelinessScore: number;
  };
  isActive: boolean;
  joinedAt: string;
}

class SpecializedServicesManager {
  
  /**
   * Créer une nouvelle commande de service spécialisé
   */
  async createServiceOrder(orderData: Omit<ServiceOrder, 'id' | 'createdAt' | 'updatedAt'>): Promise<ServiceOrder> {
    try {
      console.log('📦 Création commande service spécialisé:', orderData);

      const order = {
        ...orderData,
        id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        status: 'pending' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        trackingInfo: {
          ...orderData.trackingInfo,
          statusHistory: [
            {
              status: 'pending',
              timestamp: new Date().toISOString(),
              notes: 'Commande créée',
              updatedBy: orderData.userId,
            },
          ],
        },
      };

      const { data, error } = await supabase
        .from('specialized_service_orders')
        .insert(order)
        .select()
        .single();

      if (error) throw error;

      // Notifier le partenaire si assigné
      if (order.partnerInfo) {
        await this.notifyPartner(order.partnerInfo.partnerId, order);
      }

      return order;
    } catch (error) {
      console.error('❌ Erreur création commande service:', error);
      throw error;
    }
  }

  /**
   * Calculer le prix d'un service spécialisé
   */
  calculateServicePrice(
    service: SpecializedService,
    orderDetails: {
      items: OrderItem[];
      distance?: number;
      weight?: number;
      urgentDelivery?: boolean;
      specialRequirements?: string[];
    }
  ): { basePrice: number; additionalFees: Record<string, number>; totalPrice: number } {
    let basePrice = service.pricing.basePrice;
    const additionalFees: Record<string, number> = {};

    // Calcul selon le modèle de tarification
    switch (service.pricing.pricingModel) {
      case 'weight':
        if (orderDetails.weight && service.pricing.weightTiers) {
          const tier = service.pricing.weightTiers.find(
            t => orderDetails.weight! >= t.minWeight && orderDetails.weight! <= t.maxWeight
          );
          if (tier) {
            basePrice = tier.basePrice + (orderDetails.weight * tier.pricePerKg);
          }
        }
        break;

      case 'distance':
        if (orderDetails.distance && service.pricing.distanceTiers) {
          const tier = service.pricing.distanceTiers.find(
            t => orderDetails.distance! >= t.minDistance && orderDetails.distance! <= t.maxDistance
          );
          if (tier) {
            basePrice = tier.basePrice + (orderDetails.distance * tier.pricePerKm);
          }
        }
        break;

      case 'value_percentage':
        const itemsValue = orderDetails.items.reduce((sum, item) => sum + item.totalPrice, 0);
        basePrice = Math.max(basePrice, itemsValue * 0.1); // 10% de la valeur
        break;
    }

    // Frais additionnels
    if (orderDetails.urgentDelivery && service.pricing.additionalFees.expressDelivery) {
      additionalFees.expressDelivery = service.pricing.additionalFees.expressDelivery;
    }

    if (orderDetails.specialRequirements?.includes('fragile') && service.pricing.additionalFees.fragileHandling) {
      additionalFees.fragileHandling = service.pricing.additionalFees.fragileHandling;
    }

    if (orderDetails.specialRequirements?.includes('temperature_control') && service.pricing.additionalFees.temperatureControl) {
      additionalFees.temperatureControl = service.pricing.additionalFees.temperatureControl;
    }

    if (orderDetails.specialRequirements?.includes('insurance') && service.pricing.additionalFees.insurance) {
      additionalFees.insurance = service.pricing.additionalFees.insurance;
    }

    const totalAdditionalFees = Object.values(additionalFees).reduce((sum, fee) => sum + fee, 0);
    const totalPrice = basePrice + totalAdditionalFees;

    return { basePrice, additionalFees, totalPrice };
  }

  /**
   * Mettre à jour le statut d'une commande
   */
  async updateOrderStatus(
    orderId: string,
    newStatus: ServiceOrder['status'],
    updateInfo: {
      location?: { latitude: number; longitude: number };
      notes?: string;
      updatedBy: string;
      estimatedDeliveryTime?: string;
    }
  ): Promise<void> {
    try {
      const statusUpdate: StatusUpdate = {
        status: newStatus,
        timestamp: new Date().toISOString(),
        location: updateInfo.location,
        notes: updateInfo.notes,
        updatedBy: updateInfo.updatedBy,
      };

      const updateData: any = {
        status: newStatus,
        updated_at: new Date().toISOString(),
      };

      // Ajouter à l'historique des statuts
      const { data: currentOrder } = await supabase
        .from('specialized_service_orders')
        .select('tracking_info')
        .eq('id', orderId)
        .single();

      if (currentOrder) {
        const currentTrackingInfo = currentOrder.tracking_info || { statusHistory: [] };
        updateData.tracking_info = {
          ...currentTrackingInfo,
          statusHistory: [...(currentTrackingInfo.statusHistory || []), statusUpdate],
          estimatedDeliveryTime: updateInfo.estimatedDeliveryTime || currentTrackingInfo.estimatedDeliveryTime,
          currentLocation: updateInfo.location || currentTrackingInfo.currentLocation,
        };
      }

      const { error } = await supabase
        .from('specialized_service_orders')
        .update(updateData)
        .eq('id', orderId);

      if (error) throw error;

      // Notifier le client du changement de statut
      await this.notifyCustomerStatusUpdate(orderId, newStatus);

      console.log('✅ Statut commande mis à jour:', orderId, newStatus);
    } catch (error) {
      console.error('❌ Erreur mise à jour statut:', error);
      throw error;
    }
  }

  /**
   * Obtenir les commandes d'un utilisateur
   */
  async getUserOrders(userId: string, status?: ServiceOrder['status']): Promise<ServiceOrder[]> {
    try {
      let query = supabase
        .from('specialized_service_orders')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query;

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('❌ Erreur récupération commandes utilisateur:', error);
      throw error;
    }
  }

  /**
   * Rechercher des partenaires de service
   */
  async findServicePartners(
    category: string,
    serviceType: string,
    location: { latitude: number; longitude: number },
    radius: number = 10
  ): Promise<ServicePartner[]> {
    try {
      // Simuler la recherche de partenaires (à implémenter avec géolocalisation)
      const { data, error } = await supabase
        .from('service_partners')
        .select('*')
        .eq('category', category)
        .contains('service_types', [serviceType])
        .eq('is_active', true);

      if (error) throw error;

      // Filtrer par distance (à implémenter avec PostGIS)
      return data || [];
    } catch (error) {
      console.error('❌ Erreur recherche partenaires:', error);
      throw error;
    }
  }

  /**
   * Évaluer un service
   */
  async rateService(
    orderId: string,
    userId: string,
    rating: {
      overallRating: number;
      qualityRating: number;
      timelinessRating: number;
      comment?: string;
    }
  ): Promise<void> {
    try {
      const ratingData = {
        order_id: orderId,
        user_id: userId,
        overall_rating: rating.overallRating,
        quality_rating: rating.qualityRating,
        timeliness_rating: rating.timelinessRating,
        comment: rating.comment,
        created_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('service_ratings')
        .insert(ratingData);

      if (error) throw error;

      // Mettre à jour les statistiques du partenaire
      await this.updatePartnerRatings(orderId);

      console.log('✅ Évaluation service enregistrée');
    } catch (error) {
      console.error('❌ Erreur enregistrement évaluation:', error);
      throw error;
    }
  }

  /**
   * Obtenir l'historique de suivi d'une commande
   */
  async getOrderTracking(orderId: string): Promise<StatusUpdate[]> {
    try {
      const { data, error } = await supabase
        .from('specialized_service_orders')
        .select('tracking_info')
        .eq('id', orderId)
        .single();

      if (error) throw error;

      return data?.tracking_info?.statusHistory || [];
    } catch (error) {
      console.error('❌ Erreur récupération suivi:', error);
      throw error;
    }
  }

  // Méthodes privées
  private async notifyPartner(partnerId: string, order: ServiceOrder): Promise<void> {
    // Implémenter notification partenaire
    console.log('📧 Notification partenaire:', partnerId, order.id);
  }

  private async notifyCustomerStatusUpdate(orderId: string, status: string): Promise<void> {
    // Implémenter notification client
    console.log('📱 Notification client:', orderId, status);
  }

  private async updatePartnerRatings(orderId: string): Promise<void> {
    // Implémenter mise à jour ratings partenaire
    console.log('⭐ Mise à jour ratings partenaire pour commande:', orderId);
  }
}

export const specializedServicesManager = new SpecializedServicesManager();
export default specializedServicesManager;
