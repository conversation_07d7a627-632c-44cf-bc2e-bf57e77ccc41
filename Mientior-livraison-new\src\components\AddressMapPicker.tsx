import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
} from 'react-native';
import MapView, { Marker, Region } from 'react-native-maps';
import { Ionicons } from '@expo/vector-icons';

import { geocodingService } from '../services/geocodingService';
import { colors, typography, spacing } from '../constants/theme';
import { CustomButton } from './CustomButton';

const { width, height } = Dimensions.get('window');

export interface AddressMapPickerProps {
  initialRegion?: Region;
  onLocationSelect: (location: {
    latitude: number;
    longitude: number;
    address?: string;
  }) => void;
  showCurrentLocationButton?: boolean;
  showConfirmButton?: boolean;
  height?: number;
  style?: any;
}

export const AddressMapPicker: React.FC<AddressMapPickerProps> = ({
  initialRegion,
  onLocationSelect,
  showCurrentLocationButton = true,
  showConfirmButton = true,
  height = 300,
  style,
}) => {
  const mapRef = useRef<MapView>(null);
  
  const [region, setRegion] = useState<Region>(
    initialRegion || {
      latitude: 5.3364,
      longitude: -4.0267,
      latitudeDelta: 0.1,
      longitudeDelta: 0.1,
    }
  );
  
  const [selectedLocation, setSelectedLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  
  const [gettingLocation, setGettingLocation] = useState(false);
  const [geocoding, setGeocoding] = useState(false);

  useEffect(() => {
    if (initialRegion) {
      setSelectedLocation({
        latitude: initialRegion.latitude,
        longitude: initialRegion.longitude,
      });
    }
  }, [initialRegion]);

  const handleMapPress = async (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    setSelectedLocation({ latitude, longitude });

    // Géocodage inverse optionnel
    try {
      setGeocoding(true);
      const reverseResult = await geocodingService.reverseGeocode(latitude, longitude);
      
      onLocationSelect({
        latitude,
        longitude,
        address: reverseResult?.formatted_address,
      });
    } catch (error) {
      console.warn('Géocodage inverse échoué:', error);
      onLocationSelect({ latitude, longitude });
    } finally {
      setGeocoding(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      setGettingLocation(true);
      
      const permissions = await geocodingService.requestLocationPermissions();
      if (!permissions.granted) {
        Alert.alert(
          'Permission requise',
          'Veuillez autoriser l\'accès à la localisation pour utiliser cette fonctionnalité'
        );
        return;
      }

      const location = await geocodingService.getCurrentLocation();
      if (!location) {
        Alert.alert('Erreur', 'Impossible d\'obtenir votre position actuelle');
        return;
      }

      const newRegion = {
        latitude: location.latitude,
        longitude: location.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };

      setRegion(newRegion);
      setSelectedLocation(location);
      
      if (mapRef.current) {
        mapRef.current.animateToRegion(newRegion, 1000);
      }

      // Géocodage inverse pour obtenir l'adresse
      try {
        setGeocoding(true);
        const reverseResult = await geocodingService.reverseGeocode(
          location.latitude,
          location.longitude
        );
        
        onLocationSelect({
          ...location,
          address: reverseResult?.formatted_address,
        });
      } catch (error) {
        console.warn('Géocodage inverse échoué:', error);
        onLocationSelect(location);
      } finally {
        setGeocoding(false);
      }
    } catch (error) {
      console.error('Erreur géolocalisation:', error);
      Alert.alert('Erreur', 'Impossible d\'obtenir votre position');
    } finally {
      setGettingLocation(false);
    }
  };

  const confirmLocation = () => {
    if (selectedLocation) {
      onLocationSelect(selectedLocation);
    }
  };

  return (
    <View style={[styles.container, { height }, style]}>
      <MapView
        ref={mapRef}
        style={styles.map}
        region={region}
        onPress={handleMapPress}
        onRegionChangeComplete={setRegion}
        showsUserLocation
        showsMyLocationButton={false}
        showsCompass
        showsScale
        mapType="standard"
      >
        {selectedLocation && (
          <Marker
            coordinate={selectedLocation}
            title="Adresse sélectionnée"
            description="Appuyez pour confirmer cette position"
          >
            <View style={styles.customMarker}>
              <Ionicons name="location" size={30} color={colors.primary[500]} />
            </View>
          </Marker>
        )}
      </MapView>

      {/* Overlay Controls */}
      <View style={styles.overlayContainer}>
        {/* Current Location Button */}
        {showCurrentLocationButton && (
          <TouchableOpacity
            style={[styles.locationButton, gettingLocation && styles.buttonDisabled]}
            onPress={getCurrentLocation}
            disabled={gettingLocation}
          >
            {gettingLocation ? (
              <ActivityIndicator size="small" color={colors.text.inverse} />
            ) : (
              <Ionicons name="locate" size={20} color={colors.text.inverse} />
            )}
          </TouchableOpacity>
        )}

        {/* Geocoding Indicator */}
        {geocoding && (
          <View style={styles.geocodingIndicator}>
            <ActivityIndicator size="small" color={colors.primary[500]} />
            <Text style={styles.geocodingText}>Recherche de l'adresse...</Text>
          </View>
        )}
      </View>

      {/* Confirm Button */}
      {showConfirmButton && selectedLocation && (
        <View style={styles.confirmContainer}>
          <CustomButton
            title="Confirmer cette position"
            onPress={confirmLocation}
            leftIcon="checkmark"
            size="sm"
          />
        </View>
      )}

      {/* Instructions */}
      <View style={styles.instructionsContainer}>
        <Text style={styles.instructionsText}>
          Appuyez sur la carte pour sélectionner une adresse
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: colors.neutral[100],
  },
  map: {
    flex: 1,
  },
  customMarker: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  overlayContainer: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    gap: spacing.sm,
  },
  locationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  geocodingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 20,
    elevation: 2,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    gap: spacing.xs,
  },
  geocodingText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  confirmContainer: {
    position: 'absolute',
    bottom: spacing.lg,
    left: spacing.md,
    right: spacing.md,
  },
  instructionsContainer: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
    backgroundColor: colors.background.secondary + 'E6',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 8,
    maxWidth: width * 0.6,
  },
  instructionsText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    textAlign: 'center',
  },
});

export default AddressMapPicker;
