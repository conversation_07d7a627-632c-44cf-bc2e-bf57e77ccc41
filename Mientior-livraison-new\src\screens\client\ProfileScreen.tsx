import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  RefreshControl,
  StatusBar,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

// Hooks et services
import { useAuth } from '../../hooks/useAuth';
import { colors, typography, spacing } from '../../constants/theme';

interface ProfileOption {
  id: string;
  title: string;
  subtitle?: string;
  icon: keyof typeof Ionicons.glyphMap;
  action: () => void;
  showArrow?: boolean;
  color?: string;
}

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user, signOut, loading } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    // TODO: Recharger les données utilisateur
    setTimeout(() => setRefreshing(false), 1000);
  };

  const handleSignOut = () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Déconnexion',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
            } catch (error) {
              Alert.alert('Erreur', 'Impossible de se déconnecter');
            }
          },
        },
      ]
    );
  };

  const profileOptions: ProfileOption[] = [
    {
      id: 'edit-profile',
      title: 'Modifier le profil',
      subtitle: 'Nom, email, téléphone',
      icon: 'person-outline',
      action: () => {
        navigation.navigate('EditProfile' as never);
      },
      showArrow: true,
    },
    {
      id: 'addresses',
      title: 'Mes adresses',
      subtitle: 'Gérer vos adresses de livraison',
      icon: 'location-outline',
      action: () => {
        navigation.navigate('Addresses' as never);
      },
      showArrow: true,
    },
    {
      id: 'payment',
      title: 'Moyens de paiement',
      subtitle: 'Cartes et portefeuilles',
      icon: 'card-outline',
      action: () => {
        // TODO: Navigation vers PaymentMethodsScreen
        Alert.alert('À venir', 'Fonctionnalité en développement');
      },
      showArrow: true,
    },
    {
      id: 'notifications',
      title: 'Notifications',
      subtitle: 'Gérer vos préférences',
      icon: 'notifications-outline',
      action: () => {
        navigation.navigate('Preferences' as never);
      },
      showArrow: true,
    },
    {
      id: 'help',
      title: 'Aide et support',
      subtitle: 'FAQ, contact',
      icon: 'help-circle-outline',
      action: () => {
        (navigation as any).navigate('Help');
      },
      showArrow: true,
    },
    {
      id: 'about',
      title: 'À propos',
      subtitle: 'Version, conditions d\'utilisation',
      icon: 'information-circle-outline',
      action: () => {
        // TODO: Navigation vers AboutScreen
        Alert.alert('À venir', 'Fonctionnalité en développement');
      },
      showArrow: true,
    },
    {
      id: 'signout',
      title: 'Déconnexion',
      icon: 'log-out-outline',
      action: handleSignOut,
      showArrow: false,
      color: colors.error,
    },
  ];

  const renderProfileHeader = () => (
    <View style={styles.header}>
      <View style={styles.profileImageContainer}>
        {user?.avatar_url ? (
          <Image source={{ uri: user.avatar_url }} style={styles.profileImage} />
        ) : (
          <View style={styles.profileImagePlaceholder}>
            <Ionicons name="person" size={40} color={colors.text.secondary} />
          </View>
        )}
        <TouchableOpacity
          style={styles.editImageButton}
          onPress={() => navigation.navigate('EditProfile' as never)}
        >
          <Ionicons name="camera" size={16} color={colors.text.inverse} />
        </TouchableOpacity>
      </View>

      <View style={styles.userInfo}>
        <Text style={styles.userName}>
          {user?.full_name || 'Utilisateur'}
        </Text>
        <Text style={styles.userEmail}>
          {user?.email || '<EMAIL>'}
        </Text>
        <Text style={styles.userPhone}>
          {user?.phone || '+225 XX XX XX XX XX'}
        </Text>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>0</Text>
          <Text style={styles.statLabel}>Commandes</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>0</Text>
          <Text style={styles.statLabel}>Favoris</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>0</Text>
          <Text style={styles.statLabel}>Points</Text>
        </View>
      </View>
    </View>
  );

  const renderProfileOption = (option: ProfileOption) => (
    <TouchableOpacity
      key={option.id}
      style={styles.optionItem}
      onPress={option.action}
      activeOpacity={0.7}
    >
      <View style={styles.optionLeft}>
        <View style={[styles.optionIcon, option.color && { backgroundColor: `${option.color}15` }]}>
          <Ionicons
            name={option.icon}
            size={24}
            color={option.color || colors.text.secondary}
          />
        </View>
        <View style={styles.optionText}>
          <Text style={[styles.optionTitle, option.color && { color: option.color }]}>
            {option.title}
          </Text>
          {option.subtitle && (
            <Text style={styles.optionSubtitle}>{option.subtitle}</Text>
          )}
        </View>
      </View>
      {option.showArrow && (
        <Ionicons name="chevron-forward" size={20} color={colors.text.secondary} />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderProfileHeader()}

        <View style={styles.optionsContainer}>
          {profileOptions.map(renderProfileOption)}
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Version 1.0.0 • Livraison Afrique
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100, // Pour éviter que le contenu soit caché par la tab bar
  },
  header: {
    backgroundColor: colors.surface.primary,
    marginTop: 50, // FIX: Add top margin for status bar visibility
    paddingTop: 30, // FIX: Reduced from 60 to 30 since we added marginTop
    paddingHorizontal: 20,
    paddingBottom: 30,
    alignItems: 'center',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: colors.primary[500],
  },
  profileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: colors.primary[500],
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: colors.surface.primary,
  },
  userInfo: {
    alignItems: 'center',
    marginBottom: 24,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    color: colors.text.secondary,
    marginBottom: 2,
  },
  userPhone: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.primary[500],
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: colors.border.light,
    marginHorizontal: 16,
  },
  optionsContainer: {
    marginTop: 24,
    paddingHorizontal: 20,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.surface.primary,
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 12,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionText: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 2,
  },
  optionSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 20,
  },
  footerText: {
    fontSize: 12,
    color: colors.text.secondary,
    textAlign: 'center',
  },
});

export default ProfileScreen;