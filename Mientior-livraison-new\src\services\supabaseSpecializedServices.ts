// Service Supabase intégré pour les services spécialisés dynamiques
import { supabase } from './supabase';
import { SpecializedServiceCategory, SpecializedService } from '../types/specializedServices';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface DatabaseServiceCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  display_order: number;
  is_active: boolean;
  special_requirements: string[];
  partner_requirements: string[];
  created_at: string;
  updated_at: string;
}

export interface DatabaseService {
  id: string;
  category_id: string;
  name: string;
  description: string;
  icon: string;
  display_order: number;
  is_active: boolean;
  base_price: number;
  currency: string;
  pricing_model: string;
  weight_tiers: any[];
  size_tiers: any[];
  distance_tiers: any[];
  subscription_plans: any[];
  additional_fees: Record<string, number>;
  discounts: Record<string, number>;
  available_cities: string[];
  time_slots: any[];
  max_delivery_time: number;
  min_delivery_time: number;
  emergency_available: boolean;
  subscription_available: boolean;
  fragile_items: boolean;
  temperature_control: boolean;
  secure_transport: boolean;
  special_packaging: boolean;
  signature_required: boolean;
  photo_proof: boolean;
  real_time_tracking: boolean;
  insurance_coverage: boolean;
  custom_instructions: boolean;
  partner_type: string;
  verification_required: boolean;
  inventory_integration: boolean;
  appointment_booking: boolean;
  custom_workflow: boolean;
  quality_standards: string[];
  certification_required: string[];
  features: string[];
  requirements: string[];
  created_at: string;
  updated_at: string;
}

export interface ServiceSearchFilters {
  search_query?: string;
  category_id?: string;
  city?: string;
  price_min?: number;
  price_max?: number;
  features?: string[];
  partner_type?: string;
}

export interface PriceCalculationParams {
  service_id: string;
  weight_kg?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  distance_km?: number;
  item_value?: number;
  selected_options?: string[];
  urgent_delivery?: boolean;
  pickup_required?: boolean;
}

class SupabaseSpecializedServices {
  private categoriesCache: SpecializedServiceCategory[] | null = null;
  private servicesCache: Map<string, SpecializedService[]> = new Map();
  private cacheExpiry = 30 * 60 * 1000; // 30 minutes
  private lastCacheUpdate = 0;

  /**
   * Charger toutes les catégories de services depuis Supabase
   */
  async loadServiceCategories(forceRefresh: boolean = false): Promise<SpecializedServiceCategory[]> {
    try {
      // Vérifier le cache
      if (!forceRefresh && this.categoriesCache && this.isCacheValid()) {
        console.log('✅ Catégories chargées depuis le cache');
        return this.categoriesCache;
      }

      console.log('🔄 Chargement catégories depuis Supabase...');

      const { data: categoriesData, error } = await supabase
        .from('specialized_service_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      if (error) {
        console.error('❌ Erreur chargement catégories:', error);
        throw error;
      }

      // Convertir les données de la DB vers le format de l'application
      const categories: SpecializedServiceCategory[] = (categoriesData || []).map(this.mapCategoryFromDB);

      // Charger les services pour chaque catégorie
      for (const category of categories) {
        const services = await this.loadServicesForCategory(category.id);
        category.services = services;
      }

      // Mettre en cache
      this.categoriesCache = categories;
      this.lastCacheUpdate = Date.now();

      console.log(`✅ ${categories.length} catégories chargées depuis Supabase`);
      return categories;
    } catch (error) {
      console.error('❌ Erreur chargement catégories:', error);
      
      // Fallback vers le cache si disponible
      if (this.categoriesCache) {
        console.log('⚠️ Utilisation du cache en fallback');
        return this.categoriesCache;
      }
      
      throw error;
    }
  }

  /**
   * Charger les services pour une catégorie spécifique
   */
  async loadServicesForCategory(categoryId: string, forceRefresh: boolean = false): Promise<SpecializedService[]> {
    try {
      // Vérifier le cache
      if (!forceRefresh && this.servicesCache.has(categoryId) && this.isCacheValid()) {
        return this.servicesCache.get(categoryId)!;
      }

      console.log(`🔄 Chargement services pour catégorie ${categoryId}...`);

      const { data: servicesData, error } = await supabase
        .from('specialized_services')
        .select('*')
        .eq('category_id', categoryId)
        .eq('is_active', true)
        .order('display_order');

      if (error) {
        console.error('❌ Erreur chargement services:', error);
        throw error;
      }

      const services: SpecializedService[] = (servicesData || []).map(this.mapServiceFromDB);

      // Mettre en cache
      this.servicesCache.set(categoryId, services);

      console.log(`✅ ${services.length} services chargés pour catégorie ${categoryId}`);
      return services;
    } catch (error) {
      console.error('❌ Erreur chargement services:', error);
      
      // Fallback vers le cache si disponible
      if (this.servicesCache.has(categoryId)) {
        return this.servicesCache.get(categoryId)!;
      }
      
      return [];
    }
  }

  /**
   * Rechercher des services avec filtres
   */
  async searchServices(filters: ServiceSearchFilters): Promise<SpecializedService[]> {
    try {
      console.log('🔍 Recherche services avec filtres:', filters);

      const { data, error } = await supabase
        .rpc('search_specialized_services', {
          search_query: filters.search_query || null,
          category_id: filters.category_id || null,
          city: filters.city || null,
          price_min: filters.price_min || null,
          price_max: filters.price_max || null,
          features: filters.features || null,
          partner_type: filters.partner_type || null,
        });

      if (error) {
        console.error('❌ Erreur recherche services:', error);
        throw error;
      }

      const services: SpecializedService[] = (data || []).map(this.mapServiceFromSearchResult);

      console.log(`✅ ${services.length} services trouvés`);
      return services;
    } catch (error) {
      console.error('❌ Erreur recherche services:', error);
      return [];
    }
  }

  /**
   * Calculer le prix d'un service
   */
  async calculateServicePrice(params: PriceCalculationParams): Promise<any> {
    try {
      console.log('💰 Calcul prix service:', params);

      const { data, error } = await supabase
        .rpc('calculate_service_price', {
          service_id: params.service_id,
          weight_kg: params.weight_kg || null,
          dimensions: params.dimensions || null,
          distance_km: params.distance_km || null,
          item_value: params.item_value || null,
          selected_options: params.selected_options || [],
          urgent_delivery: params.urgent_delivery || false,
          pickup_required: params.pickup_required || false,
        });

      if (error) {
        console.error('❌ Erreur calcul prix:', error);
        throw error;
      }

      console.log('✅ Prix calculé:', data);
      return data;
    } catch (error) {
      console.error('❌ Erreur calcul prix:', error);
      throw error;
    }
  }

  /**
   * Obtenir un service par ID
   */
  async getServiceById(serviceId: string): Promise<SpecializedService | null> {
    try {
      const { data, error } = await supabase
        .from('specialized_services')
        .select('*')
        .eq('id', serviceId)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('❌ Erreur récupération service:', error);
        return null;
      }

      return this.mapServiceFromDB(data);
    } catch (error) {
      console.error('❌ Erreur récupération service:', error);
      return null;
    }
  }

  /**
   * Obtenir une catégorie par ID
   */
  async getCategoryById(categoryId: string): Promise<SpecializedServiceCategory | null> {
    try {
      const { data, error } = await supabase
        .from('specialized_service_categories')
        .select('*')
        .eq('id', categoryId)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('❌ Erreur récupération catégorie:', error);
        return null;
      }

      const category = this.mapCategoryFromDB(data);
      category.services = await this.loadServicesForCategory(categoryId);

      return category;
    } catch (error) {
      console.error('❌ Erreur récupération catégorie:', error);
      return null;
    }
  }

  /**
   * Mapper une catégorie depuis la base de données
   */
  private mapCategoryFromDB(data: DatabaseServiceCategory): SpecializedServiceCategory {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      icon: data.icon,
      color: data.color,
      specialRequirements: data.special_requirements || [],
      partnerRequirements: data.partner_requirements || [],
      services: [], // Sera rempli séparément
    };
  }

  /**
   * Mapper un service depuis la base de données
   */
  private mapServiceFromDB(data: DatabaseService): SpecializedService {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      icon: data.icon,
      category: data.category_id,
      pricing: {
        basePrice: data.base_price,
        currency: data.currency,
        pricingModel: data.pricing_model as any,
        weightTiers: data.weight_tiers || [],
        sizeTiers: data.size_tiers || [],
        distanceTiers: data.distance_tiers || [],
        subscriptionPlans: data.subscription_plans || [],
        additionalFees: data.additional_fees || {},
        discounts: data.discounts || {},
      },
      features: data.features || [],
      requirements: data.requirements || [],
      availability: {
        cities: data.available_cities || [],
        timeSlots: data.time_slots || [],
        maxDeliveryTime: data.max_delivery_time,
        minDeliveryTime: data.min_delivery_time,
        emergencyAvailable: data.emergency_available,
        subscriptionAvailable: data.subscription_available,
      },
      specialHandling: {
        fragileItems: data.fragile_items,
        temperatureControl: data.temperature_control,
        secureTransport: data.secure_transport,
        specialPackaging: data.special_packaging,
        signatureRequired: data.signature_required,
        photoProof: data.photo_proof,
        realTimeTracking: data.real_time_tracking,
        insuranceCoverage: data.insurance_coverage,
        customInstructions: data.custom_instructions,
      },
      partnerIntegration: {
        partnerType: data.partner_type,
        verificationRequired: data.verification_required,
        inventoryIntegration: data.inventory_integration,
        appointmentBooking: data.appointment_booking,
        customWorkflow: data.custom_workflow,
        qualityStandards: data.quality_standards || [],
        certificationRequired: data.certification_required || [],
      },
    };
  }

  /**
   * Mapper un service depuis les résultats de recherche
   */
  private mapServiceFromSearchResult(data: any): SpecializedService {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      icon: data.icon,
      category: data.category_id,
      pricing: {
        basePrice: data.base_price,
        currency: data.currency,
        pricingModel: data.pricing_model as any,
        weightTiers: [],
        sizeTiers: [],
        distanceTiers: [],
        subscriptionPlans: [],
        additionalFees: {},
        discounts: {},
      },
      features: data.features || [],
      requirements: [],
      availability: {
        cities: [],
        timeSlots: [],
        maxDeliveryTime: 0,
        minDeliveryTime: 0,
        emergencyAvailable: false,
        subscriptionAvailable: false,
      },
      specialHandling: {
        fragileItems: false,
        temperatureControl: false,
        secureTransport: false,
        specialPackaging: false,
        signatureRequired: false,
        photoProof: false,
        realTimeTracking: false,
        insuranceCoverage: false,
        customInstructions: false,
      },
      partnerIntegration: {
        partnerType: data.partner_type,
        verificationRequired: false,
        inventoryIntegration: false,
        appointmentBooking: false,
        customWorkflow: false,
        qualityStandards: [],
        certificationRequired: [],
      },
    };
  }

  /**
   * Vérifier si le cache est valide
   */
  private isCacheValid(): boolean {
    return Date.now() - this.lastCacheUpdate < this.cacheExpiry;
  }

  /**
   * Vider le cache
   */
  clearCache(): void {
    this.categoriesCache = null;
    this.servicesCache.clear();
    this.lastCacheUpdate = 0;
    console.log('✅ Cache Supabase vidé');
  }

  /**
   * S'abonner aux mises à jour en temps réel
   */
  subscribeToUpdates(callback: (payload: any) => void): () => void {
    const categoriesSubscription = supabase
      .channel('specialized_service_categories_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'specialized_service_categories' },
        (payload) => {
          console.log('🔄 Mise à jour catégories:', payload);
          this.clearCache();
          callback(payload);
        }
      )
      .subscribe();

    const servicesSubscription = supabase
      .channel('specialized_services_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'specialized_services' },
        (payload) => {
          console.log('🔄 Mise à jour services:', payload);
          this.clearCache();
          callback(payload);
        }
      )
      .subscribe();

    // Retourner une fonction de nettoyage
    return () => {
      supabase.removeChannel(categoriesSubscription);
      supabase.removeChannel(servicesSubscription);
    };
  }
}

export const supabaseSpecializedServices = new SupabaseSpecializedServices();
export default supabaseSpecializedServices;
