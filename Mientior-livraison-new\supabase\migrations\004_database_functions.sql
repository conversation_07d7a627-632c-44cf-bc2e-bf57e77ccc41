-- Migration: Database Functions
-- Description: Create database functions for complex pricing calculations and business logic
-- Date: 2024-12-19

-- =====================================================
-- PRICING CALCULATION FUNCTIONS
-- =====================================================

-- Function to calculate service price based on parameters
CREATE OR REPLACE FUNCTION calculate_service_price(
    service_id UUID,
    weight_kg DECIMAL DEFAULT NULL,
    dimensions JSONB DEFAULT NULL,
    distance_km DECIMAL DEFAULT NULL,
    item_value DECIMAL DEFAULT NULL,
    selected_options TEXT[] DEFAULT ARRAY[]::TEXT[],
    urgent_delivery BOOLEAN DEFAULT FALSE,
    pickup_required BOOLEAN DEFAULT FALSE
)
RETURNS JSONB AS $$
DECLARE
    service_record specialized_services%ROWTYPE;
    base_price DECIMAL := 0;
    distance_fee DECIMAL := 0;
    weight_fee DECIMAL := 0;
    size_fee DECIMAL := 0;
    value_fee DECIMAL := 0;
    options_fee DECIMAL := 0;
    urgent_fee DECIMAL := 0;
    pickup_fee DECIMAL := 0;
    total_price DECIMAL := 0;
    pricing_breakdown JSONB;
    tier JSONB;
    option_name TEXT;
BEGIN
    -- Get service details
    SELECT * INTO service_record FROM specialized_services WHERE id = service_id AND is_active = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Service not found or inactive: %', service_id;
    END IF;
    
    -- Calculate base price based on pricing model
    CASE service_record.pricing_model
        WHEN 'fixed' THEN
            base_price := service_record.base_price;
            
        WHEN 'weight' THEN
            base_price := service_record.base_price;
            IF weight_kg IS NOT NULL AND service_record.weight_tiers IS NOT NULL THEN
                FOR tier IN SELECT * FROM jsonb_array_elements(service_record.weight_tiers)
                LOOP
                    IF weight_kg >= (tier->>'minWeight')::DECIMAL AND weight_kg <= (tier->>'maxWeight')::DECIMAL THEN
                        base_price := (tier->>'basePrice')::DECIMAL + 
                                    (weight_kg * (tier->>'pricePerKg')::DECIMAL);
                        EXIT;
                    END IF;
                END LOOP;
            END IF;
            
        WHEN 'distance' THEN
            base_price := service_record.base_price;
            IF distance_km IS NOT NULL AND service_record.distance_tiers IS NOT NULL THEN
                FOR tier IN SELECT * FROM jsonb_array_elements(service_record.distance_tiers)
                LOOP
                    IF distance_km >= (tier->>'minDistance')::DECIMAL AND distance_km <= (tier->>'maxDistance')::DECIMAL THEN
                        base_price := (tier->>'basePrice')::DECIMAL + 
                                    (distance_km * (tier->>'pricePerKm')::DECIMAL);
                        EXIT;
                    END IF;
                END LOOP;
            END IF;
            
        WHEN 'value_percentage' THEN
            IF item_value IS NOT NULL THEN
                base_price := GREATEST(service_record.base_price, item_value * 0.1);
            ELSE
                base_price := service_record.base_price;
            END IF;
            
        ELSE
            base_price := service_record.base_price;
    END CASE;
    
    -- Calculate additional fees
    IF distance_km IS NOT NULL AND service_record.pricing_model != 'distance' THEN
        -- Extra distance fee (beyond 5km free)
        IF distance_km > 5 THEN
            distance_fee := (distance_km - 5) * 200; -- 200 XOF per extra km
        END IF;
    END IF;
    
    IF weight_kg IS NOT NULL AND service_record.pricing_model != 'weight' THEN
        -- Extra weight fee (beyond 2kg free)
        IF weight_kg > 2 THEN
            weight_fee := (weight_kg - 2) * 500; -- 500 XOF per extra kg
        END IF;
    END IF;
    
    -- Calculate options fees
    IF selected_options IS NOT NULL AND service_record.additional_fees IS NOT NULL THEN
        FOREACH option_name IN ARRAY selected_options
        LOOP
            IF service_record.additional_fees ? option_name THEN
                options_fee := options_fee + (service_record.additional_fees->>option_name)::DECIMAL;
            END IF;
        END LOOP;
    END IF;
    
    -- Urgent delivery fee
    IF urgent_delivery AND service_record.additional_fees ? 'express_delivery' THEN
        urgent_fee := (service_record.additional_fees->>'express_delivery')::DECIMAL;
    END IF;
    
    -- Pickup fee
    IF pickup_required AND service_record.additional_fees ? 'pickup_service' THEN
        pickup_fee := (service_record.additional_fees->>'pickup_service')::DECIMAL;
    END IF;
    
    -- Calculate total
    total_price := base_price + distance_fee + weight_fee + size_fee + value_fee + 
                   options_fee + urgent_fee + pickup_fee;
    
    -- Build pricing breakdown
    pricing_breakdown := jsonb_build_object(
        'basePrice', base_price,
        'distanceFee', distance_fee,
        'weightFee', weight_fee,
        'sizeFee', size_fee,
        'valueFee', value_fee,
        'optionsFee', options_fee,
        'urgentFee', urgent_fee,
        'pickupFee', pickup_fee,
        'subtotal', total_price,
        'taxes', total_price * 0.18, -- 18% VAT
        'totalPrice', total_price * 1.18,
        'currency', service_record.currency,
        'pricingModel', service_record.pricing_model
    );
    
    RETURN pricing_breakdown;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SERVICE SEARCH AND FILTERING FUNCTIONS
-- =====================================================

-- Function to search services with filters
CREATE OR REPLACE FUNCTION search_specialized_services(
    search_query TEXT DEFAULT NULL,
    category_id UUID DEFAULT NULL,
    city TEXT DEFAULT NULL,
    price_min DECIMAL DEFAULT NULL,
    price_max DECIMAL DEFAULT NULL,
    features TEXT[] DEFAULT NULL,
    partner_type TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    category_id UUID,
    category_name TEXT,
    name TEXT,
    description TEXT,
    icon TEXT,
    base_price DECIMAL,
    currency TEXT,
    pricing_model TEXT,
    features JSONB,
    partner_type TEXT,
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id,
        s.category_id,
        c.name as category_name,
        s.name,
        s.description,
        s.icon,
        s.base_price,
        s.currency,
        s.pricing_model,
        s.features,
        s.partner_type,
        s.is_active
    FROM specialized_services s
    JOIN specialized_service_categories c ON s.category_id = c.id
    WHERE 
        s.is_active = true
        AND c.is_active = true
        AND (search_query IS NULL OR 
             s.name ILIKE '%' || search_query || '%' OR 
             s.description ILIKE '%' || search_query || '%')
        AND (category_id IS NULL OR s.category_id = search_specialized_services.category_id)
        AND (city IS NULL OR s.available_cities ? city)
        AND (price_min IS NULL OR s.base_price >= price_min)
        AND (price_max IS NULL OR s.base_price <= price_max)
        AND (partner_type IS NULL OR s.partner_type = search_specialized_services.partner_type)
        AND (features IS NULL OR s.features ?| features)
    ORDER BY c.display_order, s.display_order;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- USER ANALYTICS FUNCTIONS (ADMIN ONLY)
-- =====================================================

-- Function to get service usage statistics
CREATE OR REPLACE FUNCTION get_service_usage_stats(
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    service_id UUID,
    service_name TEXT,
    category_name TEXT,
    total_orders BIGINT,
    total_revenue DECIMAL,
    average_order_value DECIMAL,
    completion_rate DECIMAL
) AS $$
BEGIN
    -- Check if user is admin
    IF NOT is_admin() THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;
    
    RETURN QUERY
    SELECT 
        s.id as service_id,
        s.name as service_name,
        c.name as category_name,
        COUNT(o.id) as total_orders,
        COALESCE(SUM((o.pricing->>'totalPrice')::DECIMAL), 0) as total_revenue,
        COALESCE(AVG((o.pricing->>'totalPrice')::DECIMAL), 0) as average_order_value,
        COALESCE(
            COUNT(CASE WHEN o.status = 'delivered' THEN 1 END)::DECIMAL / 
            NULLIF(COUNT(o.id), 0) * 100, 
            0
        ) as completion_rate
    FROM specialized_services s
    JOIN specialized_service_categories c ON s.category_id = c.id
    LEFT JOIN specialized_service_orders o ON s.id = o.service_id 
        AND o.created_at::DATE BETWEEN start_date AND end_date
    WHERE s.is_active = true
    GROUP BY s.id, s.name, c.name
    ORDER BY total_orders DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- ORDER MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to update order status with history
CREATE OR REPLACE FUNCTION update_order_status(
    order_id UUID,
    new_status TEXT,
    notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_order specialized_service_orders%ROWTYPE;
    status_entry JSONB;
BEGIN
    -- Get current order
    SELECT * INTO current_order FROM specialized_service_orders WHERE id = order_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Order not found: %', order_id;
    END IF;
    
    -- Check permissions (user owns order, partner assigned, or admin)
    IF NOT (
        current_order.user_id = auth.uid() OR
        is_partner(current_order.assigned_partner_id) OR
        is_admin()
    ) THEN
        RAISE EXCEPTION 'Access denied. Insufficient privileges.';
    END IF;
    
    -- Create status history entry
    status_entry := jsonb_build_object(
        'status', new_status,
        'timestamp', NOW(),
        'notes', notes,
        'updated_by', auth.uid()
    );
    
    -- Update order
    UPDATE specialized_service_orders 
    SET 
        status = new_status,
        status_history = COALESCE(status_history, '[]'::jsonb) || status_entry,
        updated_at = NOW()
    WHERE id = order_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- PARTNER MATCHING FUNCTIONS
-- =====================================================

-- Function to find available partners for a service
CREATE OR REPLACE FUNCTION find_available_partners(
    service_id UUID,
    delivery_latitude DECIMAL,
    delivery_longitude DECIMAL,
    max_distance_km INTEGER DEFAULT 10
)
RETURNS TABLE (
    partner_id UUID,
    partner_name TEXT,
    distance_km DECIMAL,
    quality_score DECIMAL,
    average_rating DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id as partner_id,
        p.name as partner_name,
        ST_Distance(
            ST_Point(p.longitude, p.latitude)::geography,
            ST_Point(delivery_longitude, delivery_latitude)::geography
        ) / 1000 as distance_km,
        p.quality_score,
        p.average_rating
    FROM service_partners p
    JOIN specialized_services s ON s.id = service_id
    WHERE 
        p.is_active = true
        AND p.accepts_new_orders = true
        AND p.partner_type = s.partner_type
        AND p.supported_services ? service_id::text
        AND ST_DWithin(
            ST_Point(p.longitude, p.latitude)::geography,
            ST_Point(delivery_longitude, delivery_latitude)::geography,
            max_distance_km * 1000
        )
    ORDER BY distance_km ASC, quality_score DESC, average_rating DESC
    LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to clean up old data
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS VOID AS $$
BEGIN
    -- Only admin can run cleanup
    IF NOT is_admin() THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;
    
    -- Delete old completed orders (older than 2 years)
    DELETE FROM specialized_service_orders 
    WHERE status = 'delivered' 
    AND created_at < NOW() - INTERVAL '2 years';
    
    -- Delete inactive user addresses (not used in 1 year)
    DELETE FROM user_addresses 
    WHERE is_active = false 
    AND last_used < NOW() - INTERVAL '1 year';
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
