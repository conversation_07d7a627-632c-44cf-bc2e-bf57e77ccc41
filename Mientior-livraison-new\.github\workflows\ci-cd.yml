name: 🚀 CI/CD Pipeline - Mientior Livraison

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  release:
    types: [published]

env:
  NODE_VERSION: '18'
  EXPO_CLI_VERSION: 'latest'

jobs:
  # 🧪 Code Quality and Testing
  test:
    name: 🧪 Test & Quality Check
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📱 Setup Expo CLI
        run: npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}
        
      - name: 📦 Install dependencies
        run: |
          cd Mientior-livraison-new
          npm ci
          
      - name: 🔍 TypeScript check
        run: |
          cd Mientior-livraison-new
          npm run type-check
          
      - name: 🧹 Lint check
        run: |
          cd Mientior-livraison-new
          npm run lint
          
      - name: 🧪 Run tests
        run: |
          cd Mientior-livraison-new
          # npm run test (when tests are implemented)
          echo "Tests will be implemented in future iterations"
          
      - name: 📊 Bundle analysis
        run: |
          cd Mientior-livraison-new
          npx expo export --platform web --output-dir dist
          
      - name: 📈 Upload bundle analysis
        uses: actions/upload-artifact@v4
        with:
          name: bundle-analysis
          path: Mientior-livraison-new/dist

  # 🏗️ Build for different environments
  build-preview:
    name: 🏗️ Build Preview
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📱 Setup Expo and EAS
        run: |
          npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}
          npm install -g eas-cli
          
      - name: 📦 Install dependencies
        run: |
          cd Mientior-livraison-new
          npm ci
          
      - name: 🔐 Authenticate with Expo
        run: |
          cd Mientior-livraison-new
          eas login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
          
      - name: 🏗️ Build preview for Android
        run: |
          cd Mientior-livraison-new
          eas build --profile preview --platform android --non-interactive
        env:
          STAGING_SUPABASE_URL: ${{ secrets.STAGING_SUPABASE_URL }}
          STAGING_SUPABASE_ANON_KEY: ${{ secrets.STAGING_SUPABASE_ANON_KEY }}
          STAGING_GOOGLE_MAPS_API_KEY: ${{ secrets.STAGING_GOOGLE_MAPS_API_KEY }}

  # 🚀 Production deployment
  deploy-production:
    name: 🚀 Deploy to Production
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'release'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📱 Setup Expo and EAS
        run: |
          npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}
          npm install -g eas-cli
          
      - name: 📦 Install dependencies
        run: |
          cd Mientior-livraison-new
          npm ci
          
      - name: 🔐 Authenticate with Expo
        run: |
          cd Mientior-livraison-new
          eas login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
          
      - name: 🏗️ Build production for Android
        run: |
          cd Mientior-livraison-new
          eas build --profile production --platform android --non-interactive
        env:
          PRODUCTION_SUPABASE_URL: ${{ secrets.PRODUCTION_SUPABASE_URL }}
          PRODUCTION_SUPABASE_ANON_KEY: ${{ secrets.PRODUCTION_SUPABASE_ANON_KEY }}
          PRODUCTION_GOOGLE_MAPS_API_KEY: ${{ secrets.PRODUCTION_GOOGLE_MAPS_API_KEY }}
          PRODUCTION_SENTRY_DSN: ${{ secrets.PRODUCTION_SENTRY_DSN }}
          PRODUCTION_FIREBASE_PROJECT_ID: ${{ secrets.PRODUCTION_FIREBASE_PROJECT_ID }}
          PRODUCTION_ORANGE_MONEY_API_KEY: ${{ secrets.PRODUCTION_ORANGE_MONEY_API_KEY }}
          PRODUCTION_MTN_MONEY_API_KEY: ${{ secrets.PRODUCTION_MTN_MONEY_API_KEY }}
          PRODUCTION_WAVE_API_KEY: ${{ secrets.PRODUCTION_WAVE_API_KEY }}
          
      - name: 🏗️ Build production for iOS
        run: |
          cd Mientior-livraison-new
          eas build --profile production --platform ios --non-interactive
        env:
          PRODUCTION_SUPABASE_URL: ${{ secrets.PRODUCTION_SUPABASE_URL }}
          PRODUCTION_SUPABASE_ANON_KEY: ${{ secrets.PRODUCTION_SUPABASE_ANON_KEY }}
          PRODUCTION_GOOGLE_MAPS_API_KEY: ${{ secrets.PRODUCTION_GOOGLE_MAPS_API_KEY }}
          PRODUCTION_SENTRY_DSN: ${{ secrets.PRODUCTION_SENTRY_DSN }}
          PRODUCTION_FIREBASE_PROJECT_ID: ${{ secrets.PRODUCTION_FIREBASE_PROJECT_ID }}
          APPLE_ID: ${{ secrets.APPLE_ID }}
          ASC_APP_ID: ${{ secrets.ASC_APP_ID }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          
      - name: 📤 Submit to Google Play Store
        run: |
          cd Mientior-livraison-new
          eas submit --profile production --platform android --non-interactive
        env:
          GOOGLE_SERVICE_ACCOUNT_KEY: ${{ secrets.GOOGLE_SERVICE_ACCOUNT_KEY }}
          
      - name: 📤 Submit to Apple App Store
        run: |
          cd Mientior-livraison-new
          eas submit --profile production --platform ios --non-interactive
        env:
          APPLE_ID: ${{ secrets.APPLE_ID }}
          ASC_APP_ID: ${{ secrets.ASC_APP_ID }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          
      - name: 📊 Create deployment report
        run: |
          echo "## 🚀 Production Deployment Report" >> $GITHUB_STEP_SUMMARY
          echo "- **Release**: ${{ github.event.release.tag_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Android**: ✅ Built and submitted to Google Play Store" >> $GITHUB_STEP_SUMMARY
          echo "- **iOS**: ✅ Built and submitted to Apple App Store" >> $GITHUB_STEP_SUMMARY
          echo "- **Timestamp**: $(date -u)" >> $GITHUB_STEP_SUMMARY

  # 🔍 Security scan
  security-scan:
    name: 🔍 Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install dependencies
        run: |
          cd Mientior-livraison-new
          npm ci
          
      - name: 🔍 Run security audit
        run: |
          cd Mientior-livraison-new
          npm audit --audit-level=high
          
      - name: 🔐 Check for secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD

  # 📊 Performance monitoring
  performance-check:
    name: 📊 Performance Check
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install dependencies
        run: |
          cd Mientior-livraison-new
          npm ci
          
      - name: 📊 Bundle size check
        run: |
          cd Mientior-livraison-new
          npx expo export --platform web --output-dir dist
          du -sh dist/
          
      - name: 📈 Performance report
        run: |
          echo "## 📊 Performance Report" >> $GITHUB_STEP_SUMMARY
          echo "- **Bundle size**: $(du -sh Mientior-livraison-new/dist/ | cut -f1)" >> $GITHUB_STEP_SUMMARY
          echo "- **Dependencies**: $(cd Mientior-livraison-new && npm list --depth=0 | wc -l) packages" >> $GITHUB_STEP_SUMMARY
          echo "- **TypeScript**: ✅ No errors" >> $GITHUB_STEP_SUMMARY
