export { ImageManipulator, manipulateAsync, useImageManipulator } from './ImageManipulator';
export { SaveFormat, FlipType } from './ImageManipulator.types';
export type { SaveOptions, ImageResult } from './ImageManipulator.types';
export type { ActionResize, ActionRotate, ActionFlip, ActionCrop, ActionExtent, Action, } from './ImageManipulator.types';
export type { ImageRef } from './ImageRef';
export type { ImageManipulatorContext } from './ImageManipulatorContext';
//# sourceMappingURL=index.d.ts.map