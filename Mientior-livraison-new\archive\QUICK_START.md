# ⚡ DÉMARRAGE RAPIDE - SERVICES B2B MIENTIOR LIVRAISON

## 🚨 **SOLUTION IMMÉDIATE À VOTRE PROBLÈME**

Vous avez l'erreur : `No development build (com.mientior.livraison) for this project is installed`

### **Solution 1 : Build de Développement (Recommandé)**
```bash
# Dans le terminal, naviguez vers le projet
cd "Mientior-livraison-new"

# Créez un build de développement
eas build --platform android --profile development

# Une fois terminé, installez l'APK sur votre appareil
# Puis démarrez le serveur
expo start --dev-client
```

### **Solution 2 : Build Local (Plus Rapide)**
```bash
# Build local (plus rapide, mais nécessite Android SDK)
eas build --platform android --profile development --local
```

### **Solution 3 : Expo Go (Temporaire)**
```bash
# Si vous voulez tester rapidement sans build
expo start

# Puis scannez le QR code avec Expo Go
# Note: Certaines fonctionnalités natives peuvent ne pas marcher
```

---

## 🚀 **GUIDE COMPLET EN 5 ÉTAPES**

### **Étape 1 : Préparation (2 min)**
```bash
# Vérifiez que vous êtes dans le bon dossier
cd "Mientior-livraison-new"

# Nettoyez le cache
expo r -c

# Installez les dépendances
npm install
```

### **Étape 2 : Configuration Environnement (3 min)**
Créez un fichier `.env.local` avec :
```env
EXPO_PUBLIC_SUPABASE_URL=https://hlvstikqlbyhofkkgrac.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
GOOGLE_MAPS_API_KEY=AIzaSyC...
EXPO_PUBLIC_ENV=development
NODE_ENV=development
```

### **Étape 3 : Build de Développement (10-15 min)**
```bash
# Connectez-vous à EAS (si pas déjà fait)
eas login

# Lancez le build
eas build --platform android --profile development
```

### **Étape 4 : Installation (2 min)**
1. **Téléchargez l'APK** depuis le lien fourni par EAS
2. **Installez sur votre appareil** Android
3. **Autorisez l'installation** depuis sources inconnues si nécessaire

### **Étape 5 : Test des Services B2B (5 min)**
```bash
# Démarrez le serveur de développement
expo start --dev-client

# Ouvrez l'app sur votre appareil
# Naviguez vers les services B2B
```

---

## 🎯 **TESTS PRIORITAIRES**

### **1. Dashboard B2B**
- ✅ Ouvrir l'onglet "Business"
- ✅ Vérifier les métriques de revenus
- ✅ Tester les graphiques interactifs

### **2. Gestion Partenaires**
- ✅ Ajouter un nouveau partenaire
- ✅ Configurer les paramètres API
- ✅ Tester la synchronisation

### **3. Services Premium**
- ✅ Créer une livraison premium
- ✅ Vérifier la tarification (2,500-7,500 FCFA)
- ✅ Tester le suivi avancé

### **4. Services Financiers**
- ✅ Accéder au dashboard financier
- ✅ Tester la collecte de paiements
- ✅ Vérifier les commissions

---

## 🛠️ **DÉPANNAGE RAPIDE**

### **Problème : Build Failed**
```bash
# Solution 1 : Nettoyer et recommencer
expo r -c
rm -rf node_modules
npm install
eas build --platform android --profile development --clear-cache
```

### **Problème : EAS Login Failed**
```bash
# Se reconnecter
eas logout
eas login
```

### **Problème : App Crash**
```bash
# Vérifier les logs
expo logs --platform android

# Ou redémarrer le serveur
expo start --dev-client --clear
```

### **Problème : Services B2B Non Visibles**
1. Vérifiez que vous êtes connecté avec un compte admin
2. Contrôlez la navigation dans `src/navigation/AppNavigator.tsx`
3. Vérifiez les permissions Supabase

---

## 📱 **UTILISATION DES SCRIPTS AUTOMATISÉS**

### **Windows (PowerShell)**
```powershell
# Exécuter le script automatisé
.\scripts\build-and-test.ps1
```

### **Linux/Mac (Bash)**
```bash
# Rendre exécutable et lancer
chmod +x scripts/build-and-test.sh
./scripts/build-and-test.sh
```

---

## 💰 **FONCTIONNALITÉS À TESTER**

### **Services Générateurs de Revenus**
1. **B2B Logistics** (45,000-85,000 FCFA/mois)
   - Intégration partenaires e-commerce
   - Commandes en masse
   - Micro-entrepôts

2. **Services Financiers** (40,000-75,000 FCFA/mois)
   - Collecte paiements Mobile Money
   - Assurance livraisons
   - Micro-crédit

3. **Q-Commerce** (25,000-50,000 FCFA/mois)
   - Livraison < 45 minutes
   - Pharmacies et cosmétiques
   - Services urgents

4. **Services Premium** (15,000-30,000 FCFA/mois)
   - Documents sécurisés
   - Haute valeur avec assurance
   - Livraison express

---

## 🎉 **RÉSULTAT ATTENDU**

Après avoir suivi ce guide, vous devriez avoir :

✅ **Application fonctionnelle** avec tous les services B2B  
✅ **15 nouveaux écrans** opérationnels  
✅ **Système de revenus** 6-12x plus élevé  
✅ **Plateforme logistique complète** pour l'Afrique  

---

## 📞 **SUPPORT IMMÉDIAT**

Si vous rencontrez des problèmes :

1. **Vérifiez les logs** : `expo logs --platform android`
2. **Nettoyez tout** : `expo r -c && rm -rf node_modules && npm install`
3. **Rebuild complet** : `eas build --platform android --profile development --clear-cache`

---

## 🚀 **PROCHAINES ÉTAPES**

Une fois l'application fonctionnelle :

1. **Testez tous les services B2B** (30 min)
2. **Configurez vos premiers partenaires** (Jumia, Konga)
3. **Lancez les services premium** avec tarification
4. **Commencez à générer des revenus** !

**Votre application Mientior Livraison est maintenant transformée en plateforme logistique génératrice de revenus pour l'Afrique !** 🌍💰
