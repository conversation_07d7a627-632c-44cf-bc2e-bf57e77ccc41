// Hook pour les abonnements temps réel aux services spécialisés
import { useState, useEffect, useCallback, useRef } from 'react';
import { unifiedSpecializedServices } from '../services/unifiedSpecializedServices';
import { SpecializedServiceCategory, SpecializedService } from '../types/specializedServices';

export interface RealtimeServicesState {
  categories: SpecializedServiceCategory[];
  services: { [categoryId: string]: SpecializedService[] };
  loading: boolean;
  error: string | null;
  lastUpdate: Date | null;
  isConnected: boolean;
  mode: 'supabase' | 'static';
}

export interface RealtimeServicesActions {
  refreshCategories: () => Promise<void>;
  refreshServices: (categoryId: string) => Promise<void>;
  searchServices: (query: string, filters?: any) => Promise<SpecializedService[]>;
  switchToSupabase: () => Promise<boolean>;
  switchToStatic: () => Promise<void>;
  clearCaches: () => Promise<void>;
}

export interface UseRealtimeServicesOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableRealtime?: boolean;
  preloadServices?: boolean;
}

export function useRealtimeServices(
  options: UseRealtimeServicesOptions = {}
): [RealtimeServicesState, RealtimeServicesActions] {
  const {
    autoRefresh = true,
    refreshInterval = 5 * 60 * 1000, // 5 minutes
    enableRealtime = true,
    preloadServices = true,
  } = options;

  // État
  const [state, setState] = useState<RealtimeServicesState>({
    categories: [],
    services: {},
    loading: true,
    error: null,
    lastUpdate: null,
    isConnected: false,
    mode: 'static',
  });

  // Références
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);

  /**
   * Mettre à jour l'état de manière sécurisée
   */
  const updateState = useCallback((updates: Partial<RealtimeServicesState>) => {
    if (isMountedRef.current) {
      setState(prev => ({ ...prev, ...updates }));
    }
  }, []);

  /**
   * Charger les catégories
   */
  const loadCategories = useCallback(async (forceRefresh = false) => {
    try {
      updateState({ loading: true, error: null });

      const categories = await unifiedSpecializedServices.loadServiceCategories({
        forceRefresh,
      });

      const mode = unifiedSpecializedServices.getCurrentMode();

      updateState({
        categories,
        loading: false,
        lastUpdate: new Date(),
        isConnected: mode === 'supabase',
        mode,
      });

      // Précharger les services si demandé
      if (preloadServices && categories.length > 0) {
        await loadAllServices(categories);
      }

      console.log(`✅ ${categories.length} catégories chargées (${mode})`);
    } catch (error) {
      console.error('❌ Erreur chargement catégories:', error);
      updateState({
        loading: false,
        error: `Erreur chargement: ${error}`,
      });
    }
  }, [preloadServices, updateState]);

  /**
   * Charger tous les services pour les catégories
   */
  const loadAllServices = useCallback(async (categories: SpecializedServiceCategory[]) => {
    try {
      const servicesMap: { [categoryId: string]: SpecializedService[] } = {};

      for (const category of categories) {
        const services = await unifiedSpecializedServices.loadServicesForCategory(category.id);
        servicesMap[category.id] = services;
      }

      updateState({ services: servicesMap });
      console.log('✅ Tous les services préchargés');
    } catch (error) {
      console.error('❌ Erreur préchargement services:', error);
    }
  }, [updateState]);

  /**
   * Charger les services pour une catégorie
   */
  const loadServicesForCategory = useCallback(async (categoryId: string, forceRefresh = false) => {
    try {
      const services = await unifiedSpecializedServices.loadServicesForCategory(categoryId, {
        forceRefresh,
      });

      updateState(prev => ({
        services: {
          ...prev.services,
          [categoryId]: services,
        },
        lastUpdate: new Date(),
      }));

      console.log(`✅ ${services.length} services chargés pour catégorie ${categoryId}`);
    } catch (error) {
      console.error(`❌ Erreur chargement services catégorie ${categoryId}:`, error);
    }
  }, [updateState]);

  /**
   * Configurer les abonnements temps réel
   */
  const setupRealtimeSubscriptions = useCallback(() => {
    if (!enableRealtime) return;

    try {
      // S'abonner aux mises à jour
      const unsubscribe = unifiedSpecializedServices.subscribeToUpdates((payload) => {
        console.log('🔄 Mise à jour temps réel reçue:', payload);

        // Recharger les données selon le type de changement
        if (payload.table === 'specialized_service_categories') {
          loadCategories(true);
        } else if (payload.table === 'specialized_services') {
          // Recharger les services de la catégorie concernée
          if (payload.new?.category_id) {
            loadServicesForCategory(payload.new.category_id, true);
          }
        }

        updateState({ lastUpdate: new Date() });
      });

      unsubscribeRef.current = unsubscribe;
      console.log('✅ Abonnements temps réel configurés');
    } catch (error) {
      console.error('❌ Erreur configuration abonnements:', error);
    }
  }, [enableRealtime, loadCategories, loadServicesForCategory, updateState]);

  /**
   * Configurer le rafraîchissement automatique
   */
  const setupAutoRefresh = useCallback(() => {
    if (!autoRefresh || refreshInterval <= 0) return;

    const scheduleRefresh = () => {
      refreshTimeoutRef.current = setTimeout(async () => {
        console.log('🔄 Rafraîchissement automatique...');
        await loadCategories(true);
        scheduleRefresh(); // Programmer le prochain rafraîchissement
      }, refreshInterval);
    };

    scheduleRefresh();
    console.log(`✅ Rafraîchissement automatique configuré (${refreshInterval / 1000}s)`);
  }, [autoRefresh, refreshInterval, loadCategories]);

  /**
   * Actions exposées
   */
  const actions: RealtimeServicesActions = {
    refreshCategories: useCallback(async () => {
      await loadCategories(true);
    }, [loadCategories]),

    refreshServices: useCallback(async (categoryId: string) => {
      await loadServicesForCategory(categoryId, true);
    }, [loadServicesForCategory]),

    searchServices: useCallback(async (query: string, filters?: any) => {
      try {
        updateState({ loading: true });
        const results = await unifiedSpecializedServices.searchServices(query, filters);
        updateState({ loading: false });
        return results;
      } catch (error) {
        console.error('❌ Erreur recherche:', error);
        updateState({ loading: false, error: `Erreur recherche: ${error}` });
        return [];
      }
    }, [updateState]),

    switchToSupabase: useCallback(async () => {
      try {
        updateState({ loading: true });
        const success = await unifiedSpecializedServices.switchToSupabase();
        
        if (success) {
          await loadCategories(true);
          setupRealtimeSubscriptions();
          updateState({ mode: 'supabase', isConnected: true });
        }
        
        updateState({ loading: false });
        return success;
      } catch (error) {
        console.error('❌ Erreur basculement Supabase:', error);
        updateState({ loading: false, error: `Erreur basculement: ${error}` });
        return false;
      }
    }, [loadCategories, setupRealtimeSubscriptions, updateState]),

    switchToStatic: useCallback(async () => {
      try {
        updateState({ loading: true });
        
        // Nettoyer les abonnements
        if (unsubscribeRef.current) {
          unsubscribeRef.current();
          unsubscribeRef.current = null;
        }
        
        await unifiedSpecializedServices.switchToStatic();
        await loadCategories(true);
        
        updateState({ 
          loading: false, 
          mode: 'static', 
          isConnected: false 
        });
      } catch (error) {
        console.error('❌ Erreur basculement statique:', error);
        updateState({ loading: false, error: `Erreur basculement: ${error}` });
      }
    }, [loadCategories, updateState]),

    clearCaches: useCallback(async () => {
      try {
        await unifiedSpecializedServices.clearAllCaches();
        await loadCategories(true);
        console.log('✅ Caches vidés et données rechargées');
      } catch (error) {
        console.error('❌ Erreur vidage caches:', error);
      }
    }, [loadCategories]),
  };

  /**
   * Initialisation
   */
  useEffect(() => {
    let mounted = true;
    isMountedRef.current = true;

    const initialize = async () => {
      if (!mounted) return;

      try {
        // Charger les données initiales
        await loadCategories();

        // Configurer les abonnements temps réel
        setupRealtimeSubscriptions();

        // Configurer le rafraîchissement automatique
        setupAutoRefresh();
      } catch (error) {
        console.error('❌ Erreur initialisation hook:', error);
        if (mounted) {
          updateState({ 
            loading: false, 
            error: `Erreur initialisation: ${error}` 
          });
        }
      }
    };

    initialize();

    return () => {
      mounted = false;
      isMountedRef.current = false;
    };
  }, [loadCategories, setupRealtimeSubscriptions, setupAutoRefresh, updateState]);

  /**
   * Nettoyage
   */
  useEffect(() => {
    return () => {
      // Nettoyer les abonnements
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }

      // Nettoyer les timeouts
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }

      isMountedRef.current = false;
    };
  }, []);

  return [state, actions];
}

export default useRealtimeServices;
