import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuthStore } from '../../store/authStore';
import { useAddresses } from '../../hooks/useUserAddressesQuery';
import { UserAddress, userAddressService } from '../../services/userAddressService';
import { colors, typography, spacing } from '../../constants/theme';
import { CustomButton } from '../../components/CustomButton';

const AddressesScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const {
    addresses,
    loading,
    refreshing,
    error,
    refresh,
    deleteAddress,
    setDefaultAddress,
    deleting,
    settingDefault,
  } = useAddresses();

  const onRefresh = async () => {
    await refresh();
  };

  const getAddressIcon = (type: UserAddress['address_type']) => {
    switch (type) {
      case 'home': return 'home';
      case 'work': return 'business';
      case 'other': return 'location';
      default: return 'location';
    }
  };

  const getAddressIconColor = (type: UserAddress['address_type']) => {
    switch (type) {
      case 'home': return '#4CAF50';
      case 'work': return '#2196F3';
      case 'other': return '#FF9800';
      default: return '#666';
    }
  };

  const handleSetDefaultAddress = async (addressId: string) => {
    try {
      await setDefaultAddress(addressId);
      Alert.alert('Succès', 'Adresse par défaut mise à jour');
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      Alert.alert('Erreur', 'Impossible de mettre à jour l\'adresse par défaut');
    }
  };

  const handleDeleteAddress = async (addressId: string) => {
    Alert.alert(
      'Supprimer l\'adresse',
      'Êtes-vous sûr de vouloir supprimer cette adresse ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteAddress(addressId);
              Alert.alert('Succès', 'Adresse supprimée');
            } catch (error) {
              console.error('Erreur lors de la suppression:', error);
              Alert.alert('Erreur', 'Impossible de supprimer l\'adresse');
            }
          },
        },
      ]
    );
  };

  const editAddress = (address: UserAddress) => {
    navigation.navigate('AddAddress', { address });
  };

  const renderAddressItem = ({ item }: { item: UserAddress }) => (
    <View style={styles.addressCard}>
      <View style={styles.addressHeader}>
        <View style={[
          styles.addressIconContainer,
          { backgroundColor: userAddressService.getAddressTypeColor(item.address_type) + '20' }
        ]}>
          <Ionicons
            name={userAddressService.getAddressTypeIcon(item.address_type) as any}
            size={24}
            color={userAddressService.getAddressTypeColor(item.address_type)}
          />
        </View>
        <View style={styles.addressInfo}>
          <View style={styles.labelContainer}>
            <Text style={styles.addressLabel}>{item.label}</Text>
            {item.is_default && (
              <View style={styles.defaultBadge}>
                <Ionicons name="star" size={12} color={colors.warning} />
                <Text style={styles.defaultText}>Par défaut</Text>
              </View>
            )}
          </View>
          <Text style={styles.addressText}>
            {userAddressService.formatAddressForDisplay(item)}
          </Text>
          {item.details && (
            <Text style={styles.addressDetails}>{item.details}</Text>
          )}
        </View>
      </View>

      <View style={styles.addressActions}>
        {!item.is_default && (
          <TouchableOpacity
            style={[
              styles.actionButton,
              styles.defaultButton,
              settingDefault && styles.actionButtonDisabled
            ]}
            onPress={() => handleSetDefaultAddress(item.id)}
            disabled={settingDefault}
          >
            <Ionicons name="star-outline" size={18} color={colors.warning} />
            <Text style={[styles.actionText, { color: colors.warning }]}>Défaut</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => editAddress(item)}
        >
          <Ionicons name="pencil" size={18} color={colors.primary[500]} />
          <Text style={[styles.actionText, { color: colors.primary[500] }]}>Modifier</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.deleteButton,
            deleting && styles.actionButtonDisabled
          ]}
          onPress={() => handleDeleteAddress(item.id)}
          disabled={deleting}
        >
          <Ionicons name="trash-outline" size={18} color={colors.error} />
          <Text style={[styles.actionText, { color: colors.error }]}>Supprimer</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={styles.emptyIconContainer}>
        <Ionicons name="location-outline" size={80} color={colors.neutral[300]} />
      </View>
      <Text style={styles.emptyTitle}>Aucune adresse enregistrée</Text>
      <Text style={styles.emptySubtitle}>
        Ajoutez vos adresses favorites pour des livraisons plus rapides et plus pratiques
      </Text>
      <View style={styles.emptyActions}>
        <CustomButton
          title="Ajouter une adresse"
          onPress={() => navigation.navigate('AddAddress')}
          leftIcon="add"
          gradient
        />
        <Text style={styles.emptyHint}>
          💡 Astuce: Vous pouvez ajouter votre domicile, bureau et autres lieux fréquents
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.headerButton}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Mes adresses</Text>
          {addresses.length > 0 && (
            <Text style={styles.headerSubtitle}>
              {addresses.length} adresse{addresses.length > 1 ? 's' : ''} enregistrée{addresses.length > 1 ? 's' : ''}
            </Text>
          )}
        </View>
        <View style={styles.headerSpacer} />
      </View>

      {/* Content */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <Ionicons name="location" size={48} color={colors.primary[300]} />
          <Text style={styles.loadingText}>Chargement des adresses...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={colors.error} />
          <Text style={styles.errorTitle}>Erreur de chargement</Text>
          <Text style={styles.errorText}>{error}</Text>
          <CustomButton
            title="Réessayer"
            onPress={onRefresh}
            variant="outline"
            size="sm"
          />
        </View>
      ) : (
        <FlatList
          data={addresses}
          renderItem={renderAddressItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={[
            styles.listContainer,
            addresses.length === 0 && styles.emptyListContainer
          ]}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary[500]]}
              tintColor={colors.primary[500]}
            />
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
      )}

      {/* Floating Action Button */}
      {addresses.length > 0 && (
        <TouchableOpacity
          style={styles.fab}
          onPress={() => navigation.navigate('AddAddress')}
          activeOpacity={0.8}
        >
          <Ionicons name="add" size={28} color={colors.text.inverse} />
        </TouchableOpacity>
      )}

      {/* Add Button (when addresses exist) */}
      {addresses.length > 0 && (
        <TouchableOpacity
          style={styles.floatingAddButton}
          onPress={() => navigation.navigate('AddAddress')}
        >
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.background.secondary,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  headerButton: {
    padding: spacing.xs,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginTop: 2,
  },
  headerSpacer: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.md,
  },
  loadingText: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    gap: spacing.md,
  },
  errorTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    textAlign: 'center',
  },
  errorText: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  listContainer: {
    padding: spacing.md,
    flexGrow: 1,
  },
  emptyListContainer: {
    justifyContent: 'center',
  },
  separator: {
    height: spacing.sm,
  },
  addressCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.sm,
    elevation: 2,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: colors.neutral[100],
  },
  addressHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  addressIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  addressInfo: {
    flex: 1,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  addressLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginRight: spacing.sm,
  },
  defaultBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.warning + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 12,
    gap: 4,
  },
  defaultText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    color: colors.warning,
  },
  addressText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.primary,
    lineHeight: 20,
    marginBottom: spacing.xs,
  },
  addressDetails: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    fontStyle: 'italic',
  },
  addressActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.neutral[200],
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.sm,
    borderRadius: 8,
    minWidth: 80,
    justifyContent: 'center',
  },
  defaultButton: {
    backgroundColor: colors.warning + '10',
  },
  editButton: {
    backgroundColor: colors.primary[50],
  },
  deleteButton: {
    backgroundColor: colors.error + '10',
  },
  actionButtonDisabled: {
    opacity: 0.5,
  },
  actionText: {
    fontSize: typography.fontSize.sm,
    marginLeft: 4,
    fontWeight: typography.fontWeight.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  emptyIconContainer: {
    marginBottom: spacing.lg,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: spacing.xl,
  },
  emptyActions: {
    width: '100%',
    alignItems: 'center',
    gap: spacing.lg,
  },
  emptyHint: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  fab: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.xl,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});

export default AddressesScreen;
