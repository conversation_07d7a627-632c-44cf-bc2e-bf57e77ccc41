import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import MapView, { Marker } from 'react-native-maps';
import { useNavigation, useRoute } from '@react-navigation/native';
import * as Location from 'expo-location';
import { useAddresses } from '../../hooks/useUserAddressesQuery';
import { UserAddress, CreateAddressData, UpdateAddressData, userAddressService } from '../../services/userAddressService';
import { geocodingService } from '../../services/geocodingService';
import { colors, typography, spacing } from '../../constants/theme';
import { CustomTextInput } from '../../components/CustomTextInput';
import { CustomButton } from '../../components/CustomButton';

const AddAddressScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { address: editingAddress } = route.params as { address?: UserAddress } || {};

  const { createAddress, updateAddress, creating, updating } = useAddresses();

  const [formData, setFormData] = useState({
    label: editingAddress?.label || '',
    address: editingAddress?.address || '',
    details: editingAddress?.details || '',
    street: editingAddress?.street || '',
    city: editingAddress?.city || '',
    postal_code: editingAddress?.postal_code || '',
    country: editingAddress?.country || 'Côte d\'Ivoire',
    coordinates: editingAddress?.coordinates || { latitude: 5.3600, longitude: -4.0083 },
    address_type: editingAddress?.address_type || 'home' as 'home' | 'work' | 'other',
    is_default: editingAddress?.is_default || false,
  });

  const [mapRegion, setMapRegion] = useState({
    latitude: formData.coordinates.latitude,
    longitude: formData.coordinates.longitude,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });

  const [locationPermission, setLocationPermission] = useState(false);
  const [gettingLocation, setGettingLocation] = useState(false);
  const [geocoding, setGeocoding] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [addressSuggestions, setAddressSuggestions] = useState<any[]>([]);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const permissions = await geocodingService.requestLocationPermissions();
      setLocationPermission(permissions.granted);
    } catch (error) {
      console.error('Erreur permission localisation:', error);
    }
  };

  const getCurrentLocation = async () => {
    if (!locationPermission) {
      Alert.alert('Permission requise', 'Veuillez autoriser l\'accès à la localisation');
      return;
    }

    try {
      setGettingLocation(true);
      const location = await geocodingService.getCurrentLocation();

      if (!location) {
        Alert.alert('Erreur', 'Impossible d\'obtenir votre position');
        return;
      }

      const { latitude, longitude } = location;

      const newRegion = {
        latitude,
        longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };

      setMapRegion(newRegion);
      setFormData(prev => ({
        ...prev,
        coordinates: { latitude, longitude },
      }));

      // Géocodage inverse pour obtenir l'adresse
      try {
        setGeocoding(true);
        const reverseResult = await geocodingService.reverseGeocode(latitude, longitude);

        if (reverseResult) {
          setFormData(prev => ({
            ...prev,
            address: reverseResult.formatted_address,
            street: reverseResult.street || '',
            city: reverseResult.city || '',
            postal_code: reverseResult.postal_code || '',
            country: reverseResult.country || 'Côte d\'Ivoire',
          }));
        }
      } catch (geocodeError) {
        console.warn('Géocodage inverse échoué:', geocodeError);
        // Continuer sans l'adresse formatée
      } finally {
        setGeocoding(false);
      }
    } catch (error) {
      console.error('Erreur géolocalisation:', error);
      Alert.alert('Erreur', 'Impossible d\'obtenir votre position');
    } finally {
      setGettingLocation(false);
    }
  };

  // Recherche d'adresses avec autocomplétion
  const searchAddresses = async (query: string) => {
    if (query.length < 3) {
      setAddressSuggestions([]);
      return;
    }

    try {
      const suggestions = await geocodingService.searchAddresses(query);
      setAddressSuggestions(suggestions);
    } catch (error) {
      console.error('Erreur recherche adresses:', error);
      setAddressSuggestions([]);
    }
  };

  // Sélectionner une suggestion d'adresse
  const selectAddressSuggestion = (suggestion: any) => {
    setFormData(prev => ({
      ...prev,
      address: suggestion.formatted_address,
      street: suggestion.street || '',
      city: suggestion.city || '',
      postal_code: suggestion.postal_code || '',
      country: suggestion.country || 'Côte d\'Ivoire',
      coordinates: suggestion.coordinates,
    }));

    setMapRegion({
      latitude: suggestion.coordinates.latitude,
      longitude: suggestion.coordinates.longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    });

    setAddressSuggestions([]);
  };

  const onMapPress = (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    setFormData(prev => ({
      ...prev,
      coordinates: { latitude, longitude },
    }));
  };

  const validateForm = () => {
    const validation = userAddressService.validateAddressData({
      label: formData.label,
      address: formData.address,
      details: formData.details,
      coordinates: formData.coordinates,
      address_type: formData.address_type,
      is_default: formData.is_default,
      street: formData.street,
      city: formData.city,
      postal_code: formData.postal_code,
      country: formData.country,
    });

    setValidationErrors(validation.errors);

    if (!validation.isValid) {
      Alert.alert('Erreur de validation', validation.errors.join('\n'));
      return false;
    }

    return true;
  };

  const saveAddress = async () => {
    if (!validateForm()) return;

    try {
      if (editingAddress) {
        // Mise à jour d'une adresse existante
        const updateData: UpdateAddressData = {
          id: editingAddress.id,
          label: formData.label,
          address: formData.address,
          details: formData.details,
          coordinates: formData.coordinates,
          address_type: formData.address_type,
          is_default: formData.is_default,
        };
        await updateAddress(updateData);
      } else {
        // Création d'une nouvelle adresse
        const createData: CreateAddressData = {
          label: formData.label,
          address: formData.address,
          details: formData.details,
          coordinates: formData.coordinates,
          address_type: formData.address_type,
          is_default: formData.is_default,
        };
        await createAddress(createData);
      }

      Alert.alert(
        'Succès',
        editingAddress ? 'Adresse modifiée avec succès' : 'Adresse ajoutée avec succès',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Erreur sauvegarde:', error);
      Alert.alert('Erreur', 'Impossible de sauvegarder l\'adresse');
    }
  };

  const addressTypes = [
    { key: 'home', label: 'Domicile', icon: 'home' },
    { key: 'work', label: 'Bureau', icon: 'business' },
    { key: 'other', label: 'Autre', icon: 'location' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {editingAddress ? 'Modifier l\'adresse' : 'Ajouter une adresse'}
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Map */}
          <View style={styles.mapContainer}>
            <MapView
              style={styles.map}
              region={mapRegion}
              onPress={onMapPress}
              showsUserLocation={locationPermission}
            >
              <Marker
                coordinate={formData.coordinates}
                title="Adresse sélectionnée"
                pinColor="#0DCAA8"
              />
            </MapView>
            
            <TouchableOpacity
              style={styles.currentLocationButton}
              onPress={getCurrentLocation}
              disabled={gettingLocation}
            >
              <Ionicons name="locate" size={20} color="#0DCAA8" />
            </TouchableOpacity>
          </View>

          {/* Form */}
          <View style={styles.formContainer}>
            {/* Address Type */}
            <Text style={styles.sectionTitle}>Type d'adresse</Text>
            <View style={styles.typeContainer}>
              {addressTypes.map((type) => (
                <TouchableOpacity
                  key={type.key}
                  style={[
                    styles.typeButton,
                    formData.address_type === type.key && styles.typeButtonActive,
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, address_type: type.key as any }))}
                >
                  <Ionicons
                    name={type.icon as any}
                    size={20}
                    color={formData.address_type === type.key ? '#fff' : '#666'}
                  />
                  <Text
                    style={[
                      styles.typeText,
                      formData.address_type === type.key && styles.typeTextActive,
                    ]}
                  >
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Label */}
            <Text style={styles.label}>Nom de l'adresse *</Text>
            <TextInput
              style={styles.input}
              value={formData.label}
              onChangeText={(text) => setFormData(prev => ({ ...prev, label: text }))}
              placeholder="Ex: Domicile, Bureau, Chez Maman..."
              placeholderTextColor="#999"
            />

            {/* Address */}
            <Text style={styles.label}>Adresse *</Text>
            <TextInput
              style={styles.input}
              value={formData.address}
              onChangeText={(text) => setFormData(prev => ({ ...prev, address: text }))}
              placeholder="Saisissez l'adresse complète"
              placeholderTextColor="#999"
              multiline
            />

            {/* Details */}
            <Text style={styles.label}>Détails (optionnel)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.details}
              onChangeText={(text) => setFormData(prev => ({ ...prev, details: text }))}
              placeholder="Informations supplémentaires (étage, code, point de repère...)"
              placeholderTextColor="#999"
              multiline
              numberOfLines={3}
            />

            {/* Default Address */}
            <TouchableOpacity
              style={styles.defaultContainer}
              onPress={() => setFormData(prev => ({ ...prev, is_default: !prev.is_default }))}
            >
              <View style={styles.checkboxContainer}>
                <Ionicons
                  name={formData.is_default ? 'checkbox' : 'square-outline'}
                  size={24}
                  color={formData.is_default ? '#0DCAA8' : '#999'}
                />
              </View>
              <Text style={styles.defaultText}>Définir comme adresse par défaut</Text>
            </TouchableOpacity>

            {/* Coordinates Info */}
            <View style={styles.coordinatesContainer}>
              <Text style={styles.coordinatesTitle}>Coordonnées GPS</Text>
              <Text style={styles.coordinatesText}>
                Lat: {formData.coordinates.latitude.toFixed(6)}
              </Text>
              <Text style={styles.coordinatesText}>
                Lng: {formData.coordinates.longitude.toFixed(6)}
              </Text>
            </View>
          </View>
        </ScrollView>

        {/* Save Button */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.saveButton, (creating || updating) && styles.saveButtonDisabled]}
            onPress={saveAddress}
            disabled={creating || updating}
          >
            <Text style={styles.saveButtonText}>
              {creating || updating ? 'Sauvegarde...' : 'Sauvegarder l\'adresse'}
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  mapContainer: {
    height: 250,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  currentLocationButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  formContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 12,
  },
  typeContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginHorizontal: 4,
    borderRadius: 12,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  typeButtonActive: {
    backgroundColor: '#0DCAA8',
    borderColor: '#0DCAA8',
  },
  typeText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
  },
  typeTextActive: {
    color: '#fff',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginBottom: 8,
    marginTop: 16,
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#000',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  defaultContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 16,
  },
  checkboxContainer: {
    marginRight: 12,
  },
  defaultText: {
    fontSize: 16,
    color: '#000',
  },
  coordinatesContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  coordinatesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
  },
  coordinatesText: {
    fontSize: 12,
    color: '#999',
    fontFamily: 'monospace',
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  saveButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#B0B0B0',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default AddAddressScreen;
