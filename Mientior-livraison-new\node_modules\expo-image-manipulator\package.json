{"name": "expo-image-manipulator", "version": "13.1.7", "description": "Provides functions that let you manipulation images on the local file system, eg: resize, crop.", "main": "src/index.ts", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-image-manipulator"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-image-manipulator"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/imagemanipulator/", "dependencies": {"expo-image-loader": "~5.1.0"}, "devDependencies": {"expo-module-scripts": "^4.1.7"}, "peerDependencies": {"expo": "*"}, "gitHead": "49c9d53cf0a9fc8179d1c8f5268beadd141f70ca"}