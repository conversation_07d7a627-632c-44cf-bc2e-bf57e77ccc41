import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

// Hooks et services
import { useAuth } from '../../hooks/useAuth';
import { profileService, ProfileUpdateData } from '../../services/profileService';

// Composants
import { CustomTextInput } from '../../components/CustomTextInput';
import { CustomButton } from '../../components/CustomButton';

// Styles et constantes
import { colors, typography, spacing } from '../../constants/theme';

interface FormData {
  full_name: string;
  phone: string;
  email: string;
  preferred_language: string;
}

interface FormErrors {
  full_name?: string;
  phone?: string;
  email?: string;
}

const EditProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user, updateProfile } = useAuth();

  // États du formulaire
  const [formData, setFormData] = useState<FormData>({
    full_name: user?.full_name || '',
    phone: user?.phone || '',
    email: user?.email || '',
    preferred_language: user?.language_preference || 'fr',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [avatarUri, setAvatarUri] = useState<string | null>(user?.avatar_url || null);
  const [avatarLoading, setAvatarLoading] = useState(false);

  // Validation du formulaire
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    // Validation du nom complet
    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Le nom complet est requis';
      isValid = false;
    } else if (formData.full_name.trim().length < 2) {
      newErrors.full_name = 'Le nom doit contenir au moins 2 caractères';
      isValid = false;
    }

    // Validation du téléphone
    if (!formData.phone.trim()) {
      newErrors.phone = 'Le numéro de téléphone est requis';
      isValid = false;
    } else if (!/^\+?[1-9]\d{1,14}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Format de téléphone invalide';
      isValid = false;
    }

    // Validation de l'email
    if (!formData.email.trim()) {
      newErrors.email = 'L\'adresse email est requise';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Gestion de la sélection d'image
  const handleImagePicker = () => {
    Alert.alert(
      'Photo de profil',
      'Choisissez une option',
      [
        {
          text: 'Galerie',
          onPress: selectFromGallery,
        },
        {
          text: 'Caméra',
          onPress: takePhoto,
        },
        {
          text: 'Supprimer',
          onPress: removeAvatar,
          style: 'destructive',
        },
        {
          text: 'Annuler',
          style: 'cancel',
        },
      ]
    );
  };

  const selectFromGallery = async () => {
    try {
      setAvatarLoading(true);
      const imageUri = await profileService.selectImageFromGallery({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (imageUri && user?.id) {
        const avatarUrl = await profileService.uploadAvatar(imageUri, user.id);
        setAvatarUri(avatarUrl);
      }
    } catch (error) {
      console.error('Erreur sélection image:', error);
      Alert.alert('Erreur', 'Impossible de sélectionner l\'image');
    } finally {
      setAvatarLoading(false);
    }
  };

  const takePhoto = async () => {
    try {
      setAvatarLoading(true);
      const imageUri = await profileService.takePhotoWithCamera({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (imageUri && user?.id) {
        const avatarUrl = await profileService.uploadAvatar(imageUri, user.id);
        setAvatarUri(avatarUrl);
      }
    } catch (error) {
      console.error('Erreur prise photo:', error);
      Alert.alert('Erreur', 'Impossible de prendre la photo');
    } finally {
      setAvatarLoading(false);
    }
  };

  const removeAvatar = async () => {
    try {
      setAvatarLoading(true);
      if (user?.id) {
        await profileService.deleteAvatar(user.id, avatarUri || undefined);
        setAvatarUri(null);
      }
    } catch (error) {
      console.error('Erreur suppression avatar:', error);
      Alert.alert('Erreur', 'Impossible de supprimer l\'avatar');
    } finally {
      setAvatarLoading(false);
    }
  };

  // Sauvegarde du profil
  const handleSave = async () => {
    if (!validateForm() || !user?.id) return;

    try {
      setLoading(true);

      const updateData: ProfileUpdateData = {
        full_name: formData.full_name.trim(),
        phone: formData.phone.trim(),
        email: formData.email.trim(),
        preferred_language: formData.preferred_language,
        avatar_url: avatarUri,
      };

      // Mettre à jour via le service
      const updatedUser = await profileService.updateUserProfile(user.id, updateData);
      
      // Mettre à jour le store local
      await updateProfile(updatedUser);

      Alert.alert(
        'Succès',
        'Votre profil a été mis à jour avec succès',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Erreur sauvegarde profil:', error);
      Alert.alert('Erreur', 'Impossible de sauvegarder le profil');
    } finally {
      setLoading(false);
    }
  };

  // Rendu de l'avatar
  const renderAvatar = () => (
    <View style={styles.avatarContainer}>
      <TouchableOpacity onPress={handleImagePicker} style={styles.avatarTouchable}>
        {avatarLoading ? (
          <View style={styles.avatarPlaceholder}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
          </View>
        ) : avatarUri ? (
          <Image source={{ uri: avatarUri }} style={styles.avatar} />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Ionicons name="person" size={50} color={colors.text.secondary} />
          </View>
        )}
        <View style={styles.cameraIcon}>
          <Ionicons name="camera" size={20} color={colors.text.inverse} />
        </View>
      </TouchableOpacity>
      <Text style={styles.avatarText}>Touchez pour modifier</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* En-tête */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Modifier le profil</Text>
        <View style={styles.headerSpacer} />
      </View>

      <KeyboardAvoidingView 
        style={styles.flex} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Avatar */}
          {renderAvatar()}

          {/* Formulaire */}
          <View style={styles.form}>
            <CustomTextInput
              label="Nom complet"
              value={formData.full_name}
              onChangeText={(text) => setFormData({ ...formData, full_name: text })}
              error={errors.full_name}
              placeholder="Entrez votre nom complet"
              autoCapitalize="words"
            />

            <CustomTextInput
              label="Téléphone"
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              error={errors.phone}
              placeholder="+225 XX XX XX XX XX"
              keyboardType="phone-pad"
            />

            <CustomTextInput
              label="Email"
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              error={errors.email}
              placeholder="<EMAIL>"
              keyboardType="email-address"
              autoCapitalize="none"
            />

            {/* Langue préférée */}
            <View style={styles.languageContainer}>
              <Text style={styles.languageLabel}>Langue préférée</Text>
              <View style={styles.languageOptions}>
                <TouchableOpacity
                  style={[
                    styles.languageOption,
                    formData.preferred_language === 'fr' && styles.languageOptionActive,
                  ]}
                  onPress={() => setFormData({ ...formData, preferred_language: 'fr' })}
                >
                  <Text
                    style={[
                      styles.languageOptionText,
                      formData.preferred_language === 'fr' && styles.languageOptionTextActive,
                    ]}
                  >
                    Français
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.languageOption,
                    formData.preferred_language === 'en' && styles.languageOptionActive,
                  ]}
                  onPress={() => setFormData({ ...formData, preferred_language: 'en' })}
                >
                  <Text
                    style={[
                      styles.languageOptionText,
                      formData.preferred_language === 'en' && styles.languageOptionTextActive,
                    ]}
                  >
                    English
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Bouton de sauvegarde */}
          <View style={styles.buttonContainer}>
            <CustomButton
              title="Sauvegarder"
              onPress={handleSave}
              loading={loading}
              disabled={loading}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  flex: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
  },
  avatarContainer: {
    alignItems: 'center',
    marginVertical: spacing.xl,
  },
  avatarTouchable: {
    position: 'relative',
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.neutral[100],
  },
  avatarPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.neutral[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraIcon: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: colors.primary[500],
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: colors.background.primary,
  },
  avatarText: {
    marginTop: spacing.sm,
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  form: {
    gap: spacing.md,
  },
  languageContainer: {
    marginTop: spacing.sm,
  },
  languageLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  languageOptions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  languageOption: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.neutral[300],
    alignItems: 'center',
  },
  languageOptionActive: {
    backgroundColor: colors.primary[500],
    borderColor: colors.primary[500],
  },
  languageOptionText: {
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
  },
  languageOptionTextActive: {
    color: colors.text.inverse,
  },
  buttonContainer: {
    marginTop: spacing.xl,
  },
});

export default EditProfileScreen;
