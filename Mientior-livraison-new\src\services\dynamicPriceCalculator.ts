// Service de calcul dynamique de prix en temps réel
import { SpecializedService } from '../types/specializedServices';
import { DistanceCalculationResult } from './distanceCalculationService';

export interface PriceCalculationInput {
  service: SpecializedService;
  distance?: DistanceCalculationResult;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  itemValue?: number;
  selectedOptions: string[];
  urgentDelivery: boolean;
  subscriptionDiscount?: number;
  loyaltyDiscount?: number;
  promoCode?: string;
  deliveryTime?: Date;
  pickupRequired?: boolean;
}

export interface PriceBreakdown {
  basePrice: number;
  distanceFee: number;
  weightFee: number;
  sizeFee: number;
  valueFee: number;
  additionalOptions: {
    [optionName: string]: number;
  };
  urgentDeliveryFee: number;
  pickupFee: number;
  subtotal: number;
  discounts: {
    subscription?: number;
    loyalty?: number;
    bulk?: number;
    promo?: number;
    firstTime?: number;
  };
  totalDiscounts: number;
  taxes: number;
  finalTotal: number;
  currency: string;
  savings?: number;
}

export interface PriceEstimate {
  breakdown: PriceBreakdown;
  confidence: 'high' | 'medium' | 'low';
  validUntil: Date;
  alternatives?: {
    service: SpecializedService;
    price: number;
    savings: number;
  }[];
  recommendations?: string[];
}

export interface PromotionRule {
  id: string;
  code: string;
  type: 'percentage' | 'fixed_amount' | 'free_delivery';
  value: number;
  minOrderValue?: number;
  maxDiscount?: number;
  validFrom: Date;
  validUntil: Date;
  applicableServices?: string[];
  applicableCategories?: string[];
  usageLimit?: number;
  usedCount: number;
  isActive: boolean;
}

class DynamicPriceCalculator {
  private promotions: PromotionRule[] = [];
  private taxRate = 0.18; // 18% TVA en Côte d'Ivoire
  private priceCache: Map<string, { estimate: PriceEstimate; timestamp: number }> = new Map();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes

  /**
   * Calculer le prix en temps réel
   */
  async calculatePrice(input: PriceCalculationInput): Promise<PriceEstimate> {
    try {
      console.log('💰 Calcul prix dynamique pour:', input.service.name);

      // Vérifier le cache
      const cacheKey = this.createCacheKey(input);
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        console.log('✅ Prix récupéré depuis le cache');
        return cached;
      }

      // Calculer le prix de base
      const basePrice = this.calculateBasePrice(input);

      // Calculer les frais additionnels
      const distanceFee = this.calculateDistanceFee(input);
      const weightFee = this.calculateWeightFee(input);
      const sizeFee = this.calculateSizeFee(input);
      const valueFee = this.calculateValueFee(input);
      const additionalOptions = this.calculateAdditionalOptions(input);
      const urgentDeliveryFee = input.urgentDelivery ? (input.service.pricing.additionalFees.expressDelivery || 0) : 0;
      const pickupFee = input.pickupRequired ? (input.service.pricing.additionalFees.pickupService || 0) : 0;

      // Calculer le sous-total
      const subtotal = basePrice + distanceFee + weightFee + sizeFee + valueFee + 
                     Object.values(additionalOptions).reduce((sum, fee) => sum + fee, 0) +
                     urgentDeliveryFee + pickupFee;

      // Calculer les remises
      const discounts = await this.calculateDiscounts(input, subtotal);
      const totalDiscounts = Object.values(discounts).reduce((sum, discount) => sum + (discount || 0), 0);

      // Calculer les taxes
      const taxableAmount = Math.max(0, subtotal - totalDiscounts);
      const taxes = taxableAmount * this.taxRate;

      // Prix final
      const finalTotal = taxableAmount + taxes;

      const breakdown: PriceBreakdown = {
        basePrice,
        distanceFee,
        weightFee,
        sizeFee,
        valueFee,
        additionalOptions,
        urgentDeliveryFee,
        pickupFee,
        subtotal,
        discounts,
        totalDiscounts,
        taxes,
        finalTotal,
        currency: input.service.pricing.currency,
        savings: totalDiscounts,
      };

      // Générer des recommandations
      const recommendations = this.generateRecommendations(input, breakdown);

      const estimate: PriceEstimate = {
        breakdown,
        confidence: this.calculateConfidence(input),
        validUntil: new Date(Date.now() + 30 * 60 * 1000), // Valide 30 minutes
        recommendations,
      };

      // Mettre en cache
      this.addToCache(cacheKey, estimate);

      console.log(`✅ Prix calculé: ${finalTotal.toLocaleString()} ${breakdown.currency}`);
      return estimate;
    } catch (error) {
      console.error('❌ Erreur calcul prix:', error);
      throw error;
    }
  }

  /**
   * Calculer le prix de base selon le modèle de tarification
   */
  private calculateBasePrice(input: PriceCalculationInput): number {
    const { service, weight, dimensions, itemValue, distance } = input;
    const pricing = service.pricing;

    switch (pricing.pricingModel) {
      case 'fixed':
        return pricing.basePrice;

      case 'weight':
        if (!weight || !pricing.weightTiers) return pricing.basePrice;
        
        const weightTier = pricing.weightTiers.find(
          tier => weight >= tier.minWeight && weight <= tier.maxWeight
        );
        return weightTier ? weightTier.basePrice + (weight * weightTier.pricePerKg) : pricing.basePrice;

      case 'size':
        if (!dimensions || !pricing.sizeTiers) return pricing.basePrice;
        
        const volume = dimensions.length * dimensions.width * dimensions.height;
        const sizeTier = pricing.sizeTiers.find(tier => {
          const tierVolume = tier.maxDimensions.length * tier.maxDimensions.width * tier.maxDimensions.height;
          return volume <= tierVolume;
        });
        return sizeTier ? sizeTier.price : pricing.basePrice;

      case 'distance':
        if (!distance || !pricing.distanceTiers) return pricing.basePrice;
        
        const distanceKm = distance.distance.value / 1000;
        const distanceTier = pricing.distanceTiers.find(
          tier => distanceKm >= tier.minDistance && distanceKm <= tier.maxDistance
        );
        return distanceTier ? distanceTier.basePrice + (distanceKm * distanceTier.pricePerKm) : pricing.basePrice;

      case 'value_percentage':
        if (!itemValue) return pricing.basePrice;
        return Math.max(pricing.basePrice, itemValue * 0.1); // 10% de la valeur

      case 'time':
        if (!distance) return pricing.basePrice;
        const durationHours = distance.duration.value / 3600;
        return pricing.basePrice * Math.max(1, durationHours);

      default:
        return pricing.basePrice;
    }
  }

  /**
   * Calculer les frais de distance
   */
  private calculateDistanceFee(input: PriceCalculationInput): number {
    if (!input.distance || input.service.pricing.pricingModel === 'distance') {
      return 0; // Déjà inclus dans le prix de base
    }

    const distanceKm = input.distance.distance.value / 1000;
    const freeDistanceKm = 5; // 5 km gratuits
    const extraDistance = Math.max(0, distanceKm - freeDistanceKm);
    const ratePerKm = 200; // 200 XOF par km supplémentaire

    return extraDistance * ratePerKm;
  }

  /**
   * Calculer les frais de poids
   */
  private calculateWeightFee(input: PriceCalculationInput): number {
    if (!input.weight || input.service.pricing.pricingModel === 'weight') {
      return 0; // Déjà inclus dans le prix de base
    }

    const freeWeightKg = 2; // 2 kg gratuits
    const extraWeight = Math.max(0, input.weight - freeWeightKg);
    const ratePerKg = 500; // 500 XOF par kg supplémentaire

    return extraWeight * ratePerKg;
  }

  /**
   * Calculer les frais de taille
   */
  private calculateSizeFee(input: PriceCalculationInput): number {
    if (!input.dimensions || input.service.pricing.pricingModel === 'size') {
      return 0; // Déjà inclus dans le prix de base
    }

    const volume = input.dimensions.length * input.dimensions.width * input.dimensions.height;
    const freeVolume = 30 * 20 * 15; // Volume gratuit en cm³
    const extraVolume = Math.max(0, volume - freeVolume);
    const ratePer1000cm3 = 100; // 100 XOF par 1000 cm³ supplémentaires

    return Math.ceil(extraVolume / 1000) * ratePer1000cm3;
  }

  /**
   * Calculer les frais de valeur
   */
  private calculateValueFee(input: PriceCalculationInput): number {
    if (!input.itemValue || input.service.pricing.pricingModel === 'value_percentage') {
      return 0; // Déjà inclus dans le prix de base
    }

    const freeValue = 50000; // 50000 XOF gratuits
    const extraValue = Math.max(0, input.itemValue - freeValue);
    const rate = 0.005; // 0.5% de la valeur supplémentaire

    return extraValue * rate;
  }

  /**
   * Calculer les options additionnelles
   */
  private calculateAdditionalOptions(input: PriceCalculationInput): { [optionName: string]: number } {
    const options: { [optionName: string]: number } = {};
    const additionalFees = input.service.pricing.additionalFees;

    input.selectedOptions.forEach(option => {
      if (additionalFees[option]) {
        options[option] = additionalFees[option];
      }
    });

    return options;
  }

  /**
   * Calculer les remises
   */
  private async calculateDiscounts(input: PriceCalculationInput, subtotal: number): Promise<PriceBreakdown['discounts']> {
    const discounts: PriceBreakdown['discounts'] = {};

    // Remise abonnement
    if (input.subscriptionDiscount) {
      discounts.subscription = subtotal * (input.subscriptionDiscount / 100);
    }

    // Remise fidélité
    if (input.loyaltyDiscount) {
      discounts.loyalty = subtotal * (input.loyaltyDiscount / 100);
    }

    // Remise première commande
    if (input.service.pricing.discounts?.firstTime) {
      discounts.firstTime = subtotal * (input.service.pricing.discounts.firstTime / 100);
    }

    // Remise quantité
    if (input.service.pricing.discounts?.bulkOrders) {
      // Logique de remise quantité (à implémenter selon le contexte)
    }

    // Code promo
    if (input.promoCode) {
      const promoDiscount = await this.applyPromoCode(input.promoCode, subtotal, input.service);
      if (promoDiscount > 0) {
        discounts.promo = promoDiscount;
      }
    }

    return discounts;
  }

  /**
   * Appliquer un code promo
   */
  private async applyPromoCode(code: string, subtotal: number, service: SpecializedService): Promise<number> {
    const promotion = this.promotions.find(p => 
      p.code.toLowerCase() === code.toLowerCase() &&
      p.isActive &&
      new Date() >= p.validFrom &&
      new Date() <= p.validUntil &&
      (!p.minOrderValue || subtotal >= p.minOrderValue) &&
      (!p.applicableServices || p.applicableServices.includes(service.id)) &&
      (!p.usageLimit || p.usedCount < p.usageLimit)
    );

    if (!promotion) return 0;

    let discount = 0;

    switch (promotion.type) {
      case 'percentage':
        discount = subtotal * (promotion.value / 100);
        if (promotion.maxDiscount) {
          discount = Math.min(discount, promotion.maxDiscount);
        }
        break;

      case 'fixed_amount':
        discount = promotion.value;
        break;

      case 'free_delivery':
        // Calculer le coût de livraison pour l'annuler
        discount = Math.min(subtotal * 0.3, 2000); // Estimation
        break;
    }

    return discount;
  }

  /**
   * Calculer la confiance du prix
   */
  private calculateConfidence(input: PriceCalculationInput): 'high' | 'medium' | 'low' {
    let score = 100;

    // Réduire la confiance si des données manquent
    if (!input.distance) score -= 20;
    if (!input.weight && input.service.pricing.pricingModel === 'weight') score -= 30;
    if (!input.dimensions && input.service.pricing.pricingModel === 'size') score -= 30;
    if (!input.itemValue && input.service.pricing.pricingModel === 'value_percentage') score -= 25;

    if (score >= 80) return 'high';
    if (score >= 60) return 'medium';
    return 'low';
  }

  /**
   * Générer des recommandations
   */
  private generateRecommendations(input: PriceCalculationInput, breakdown: PriceBreakdown): string[] {
    const recommendations: string[] = [];

    // Recommandation abonnement
    if (!input.subscriptionDiscount && input.service.pricing.subscriptionPlans) {
      recommendations.push('Économisez jusqu\'à 20% avec un abonnement mensuel');
    }

    // Recommandation livraison groupée
    if (breakdown.finalTotal > 5000) {
      recommendations.push('Groupez vos commandes pour réduire les frais de livraison');
    }

    // Recommandation timing
    if (input.urgentDelivery && breakdown.urgentDeliveryFee > 0) {
      recommendations.push(`Économisez ${breakdown.urgentDeliveryFee} XOF en choisissant la livraison standard`);
    }

    // Recommandation options
    if (Object.keys(breakdown.additionalOptions).length > 2) {
      recommendations.push('Certaines options peuvent être incluses dans un forfait');
    }

    return recommendations;
  }

  /**
   * Créer une clé de cache
   */
  private createCacheKey(input: PriceCalculationInput): string {
    const key = {
      serviceId: input.service.id,
      distance: input.distance?.distance.value,
      weight: input.weight,
      dimensions: input.dimensions,
      itemValue: input.itemValue,
      selectedOptions: input.selectedOptions.sort(),
      urgentDelivery: input.urgentDelivery,
      pickupRequired: input.pickupRequired,
    };

    return JSON.stringify(key);
  }

  /**
   * Récupérer depuis le cache
   */
  private getFromCache(key: string): PriceEstimate | null {
    const cached = this.priceCache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.estimate;
    }
    
    if (cached) {
      this.priceCache.delete(key);
    }
    
    return null;
  }

  /**
   * Ajouter au cache
   */
  private addToCache(key: string, estimate: PriceEstimate): void {
    this.priceCache.set(key, {
      estimate,
      timestamp: Date.now(),
    });

    // Nettoyer le cache si trop volumineux
    if (this.priceCache.size > 50) {
      const oldestKey = this.priceCache.keys().next().value;
      this.priceCache.delete(oldestKey);
    }
  }

  /**
   * Charger les promotions
   */
  async loadPromotions(): Promise<void> {
    try {
      // En production, charger depuis la base de données
      // Pour l'instant, utiliser des données de test
      this.promotions = [
        {
          id: 'WELCOME20',
          code: 'WELCOME20',
          type: 'percentage',
          value: 20,
          minOrderValue: 2000,
          maxDiscount: 5000,
          validFrom: new Date('2024-01-01'),
          validUntil: new Date('2024-12-31'),
          usageLimit: 1,
          usedCount: 0,
          isActive: true,
        },
        {
          id: 'FREEDELIVERY',
          code: 'FREEDELIVERY',
          type: 'free_delivery',
          value: 0,
          minOrderValue: 10000,
          validFrom: new Date('2024-01-01'),
          validUntil: new Date('2024-12-31'),
          usedCount: 0,
          isActive: true,
        },
      ];

      console.log(`✅ ${this.promotions.length} promotions chargées`);
    } catch (error) {
      console.error('❌ Erreur chargement promotions:', error);
    }
  }

  /**
   * Vider le cache
   */
  clearCache(): void {
    this.priceCache.clear();
    console.log('✅ Cache prix vidé');
  }
}

export const dynamicPriceCalculator = new DynamicPriceCalculator();
export default dynamicPriceCalculator;
