import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

// Hooks et services
import { useAuth } from '../../hooks/useAuth';
import { utilityService } from '../../services/supabase';

// Styles et constantes
import { colors, typography, spacing } from '../../constants/theme';

interface NotificationPreferences {
  push_enabled: boolean;
  email_enabled: boolean;
  sms_enabled: boolean;
  order_updates: boolean;
  promotional: boolean;
}

const PreferencesScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user, updateProfile } = useAuth();

  // États des préférences
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    push_enabled: true,
    email_enabled: true,
    sms_enabled: false,
    order_updates: true,
    promotional: false,
  });

  const [loading, setLoading] = useState(false);

  // Charger les préférences existantes
  useEffect(() => {
    if (user?.notification_preferences) {
      setPreferences({
        push_enabled: user.notification_preferences.push_enabled ?? true,
        email_enabled: user.notification_preferences.email_enabled ?? true,
        sms_enabled: user.notification_preferences.sms_enabled ?? false,
        order_updates: user.notification_preferences.order_updates ?? true,
        promotional: user.notification_preferences.promotional ?? false,
      });
    }
  }, [user]);

  // Mettre à jour une préférence
  const updatePreference = (key: keyof NotificationPreferences, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Sauvegarder les préférences
  const savePreferences = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      
      await utilityService.updateNotificationPreferences(user.id, preferences);
      
      // Mettre à jour le profil local
      const updatedUser = {
        ...user,
        notification_preferences: preferences,
      };
      
      await updateProfile(updatedUser);

      Alert.alert('Succès', 'Vos préférences ont été sauvegardées');
    } catch (error) {
      console.error('Erreur sauvegarde préférences:', error);
      Alert.alert('Erreur', 'Impossible de sauvegarder les préférences');
    } finally {
      setLoading(false);
    }
  };

  // Rendu d'un élément de préférence
  const renderPreferenceItem = (
    key: keyof NotificationPreferences,
    title: string,
    description: string,
    icon: keyof typeof Ionicons.glyphMap
  ) => (
    <View style={styles.preferenceItem}>
      <View style={styles.preferenceIcon}>
        <Ionicons name={icon} size={24} color={colors.primary[500]} />
      </View>
      <View style={styles.preferenceContent}>
        <Text style={styles.preferenceTitle}>{title}</Text>
        <Text style={styles.preferenceDescription}>{description}</Text>
      </View>
      <Switch
        value={preferences[key]}
        onValueChange={(value) => updatePreference(key, value)}
        trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
        thumbColor={preferences[key] ? colors.primary[500] : colors.neutral[400]}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* En-tête */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Préférences</Text>
        <TouchableOpacity onPress={savePreferences} disabled={loading} style={styles.saveButton}>
          <Text style={[styles.saveButtonText, loading && styles.saveButtonTextDisabled]}>
            Sauvegarder
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Section Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          <Text style={styles.sectionDescription}>
            Gérez vos préférences de notification
          </Text>

          <View style={styles.preferencesContainer}>
            {renderPreferenceItem(
              'push_enabled',
              'Notifications push',
              'Recevoir des notifications sur votre appareil',
              'notifications'
            )}

            {renderPreferenceItem(
              'email_enabled',
              'Notifications par email',
              'Recevoir des notifications par email',
              'mail'
            )}

            {renderPreferenceItem(
              'sms_enabled',
              'Notifications SMS',
              'Recevoir des notifications par SMS',
              'chatbubble'
            )}
          </View>
        </View>

        {/* Section Types de notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Types de notifications</Text>
          <Text style={styles.sectionDescription}>
            Choisissez les types de notifications que vous souhaitez recevoir
          </Text>

          <View style={styles.preferencesContainer}>
            {renderPreferenceItem(
              'order_updates',
              'Mises à jour de commandes',
              'Notifications sur le statut de vos commandes',
              'receipt'
            )}

            {renderPreferenceItem(
              'promotional',
              'Offres promotionnelles',
              'Recevoir des offres spéciales et promotions',
              'pricetag'
            )}
          </View>
        </View>

        {/* Section Informations */}
        <View style={styles.infoSection}>
          <View style={styles.infoItem}>
            <Ionicons name="information-circle" size={20} color={colors.primary[500]} />
            <Text style={styles.infoText}>
              Vous pouvez modifier ces préférences à tout moment. 
              Les notifications importantes liées à la sécurité seront toujours envoyées.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  saveButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  saveButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.primary[500],
  },
  saveButtonTextDisabled: {
    color: colors.text.disabled,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
  },
  section: {
    marginTop: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  sectionDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: spacing.md,
  },
  preferencesContainer: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    overflow: 'hidden',
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  preferenceIcon: {
    marginRight: spacing.md,
  },
  preferenceContent: {
    flex: 1,
  },
  preferenceTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  preferenceDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  infoSection: {
    marginTop: spacing.xl,
    marginBottom: spacing.lg,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.primary[50],
    padding: spacing.md,
    borderRadius: 8,
  },
  infoText: {
    flex: 1,
    marginLeft: spacing.sm,
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 20,
  },
});

export default PreferencesScreen;
