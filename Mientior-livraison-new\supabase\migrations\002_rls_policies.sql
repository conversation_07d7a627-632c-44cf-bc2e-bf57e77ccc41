-- Migration: Row Level Security Policies
-- Description: Set up RLS policies for data protection and user access control
-- Date: 2024-12-19

-- =====================================================
-- ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE specialized_service_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialized_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialized_service_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_partners ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- SPECIALIZED SERVICE CATEGORIES POLICIES
-- =====================================================

-- Public read access for active categories
CREATE POLICY "Public can view active categories" ON specialized_service_categories
    FOR SELECT USING (is_active = true);

-- Admin full access
CREATE POLICY "Admin full access to categories" ON specialized_service_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_app_meta_data->>'role' = 'admin'
        )
    );

-- =====================================================
-- SPECIALIZED SERVICES POLICIES
-- =====================================================

-- Public read access for active services
CREATE POLICY "Public can view active services" ON specialized_services
    FOR SELECT USING (is_active = true);

-- Admin full access
CREATE POLICY "Admin full access to services" ON specialized_services
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_app_meta_data->>'role' = 'admin'
        )
    );

-- =====================================================
-- USER PREFERENCES POLICIES
-- =====================================================

-- Users can only access their own preferences
CREATE POLICY "Users can view own preferences" ON user_preferences
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert own preferences" ON user_preferences
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own preferences" ON user_preferences
    FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete own preferences" ON user_preferences
    FOR DELETE USING (user_id = auth.uid());

-- Admin can view all preferences
CREATE POLICY "Admin can view all preferences" ON user_preferences
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_app_meta_data->>'role' = 'admin'
        )
    );

-- =====================================================
-- USER ADDRESSES POLICIES
-- =====================================================

-- Users can only access their own addresses
CREATE POLICY "Users can view own addresses" ON user_addresses
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert own addresses" ON user_addresses
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own addresses" ON user_addresses
    FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete own addresses" ON user_addresses
    FOR DELETE USING (user_id = auth.uid());

-- Admin can view all addresses (for support purposes)
CREATE POLICY "Admin can view all addresses" ON user_addresses
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_app_meta_data->>'role' = 'admin'
        )
    );

-- =====================================================
-- SPECIALIZED SERVICE ORDERS POLICIES
-- =====================================================

-- Users can only access their own orders
CREATE POLICY "Users can view own orders" ON specialized_service_orders
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert own orders" ON specialized_service_orders
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own orders" ON specialized_service_orders
    FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());

-- Partners can view and update assigned orders
CREATE POLICY "Partners can view assigned orders" ON specialized_service_orders
    FOR SELECT USING (
        assigned_partner_id IN (
            SELECT id FROM service_partners 
            WHERE service_partners.id = assigned_partner_id
            AND EXISTS (
                SELECT 1 FROM auth.users 
                WHERE auth.users.id = auth.uid() 
                AND auth.users.raw_app_meta_data->>'partner_id' = assigned_partner_id::text
            )
        )
    );

CREATE POLICY "Partners can update assigned orders" ON specialized_service_orders
    FOR UPDATE USING (
        assigned_partner_id IN (
            SELECT id FROM service_partners 
            WHERE service_partners.id = assigned_partner_id
            AND EXISTS (
                SELECT 1 FROM auth.users 
                WHERE auth.users.id = auth.uid() 
                AND auth.users.raw_app_meta_data->>'partner_id' = assigned_partner_id::text
            )
        )
    ) WITH CHECK (
        assigned_partner_id IN (
            SELECT id FROM service_partners 
            WHERE service_partners.id = assigned_partner_id
            AND EXISTS (
                SELECT 1 FROM auth.users 
                WHERE auth.users.id = auth.uid() 
                AND auth.users.raw_app_meta_data->>'partner_id' = assigned_partner_id::text
            )
        )
    );

-- Admin full access to orders
CREATE POLICY "Admin full access to orders" ON specialized_service_orders
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_app_meta_data->>'role' = 'admin'
        )
    );

-- =====================================================
-- SERVICE PARTNERS POLICIES
-- =====================================================

-- Public read access for active partners (limited fields)
CREATE POLICY "Public can view active partners" ON service_partners
    FOR SELECT USING (is_active = true);

-- Partners can view and update their own profile
CREATE POLICY "Partners can view own profile" ON service_partners
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_app_meta_data->>'partner_id' = id::text
        )
    );

CREATE POLICY "Partners can update own profile" ON service_partners
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_app_meta_data->>'partner_id' = id::text
        )
    ) WITH CHECK (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_app_meta_data->>'partner_id' = id::text
        )
    );

-- Admin full access to partners
CREATE POLICY "Admin full access to partners" ON service_partners
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_app_meta_data->>'role' = 'admin'
        )
    );

-- =====================================================
-- HELPER FUNCTIONS FOR RLS
-- =====================================================

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.raw_app_meta_data->>'role' = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is partner
CREATE OR REPLACE FUNCTION is_partner(partner_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.raw_app_meta_data->>'partner_id' = partner_id::text
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's partner ID
CREATE OR REPLACE FUNCTION get_user_partner_id()
RETURNS UUID AS $$
BEGIN
    RETURN (
        SELECT (auth.users.raw_app_meta_data->>'partner_id')::UUID
        FROM auth.users 
        WHERE auth.users.id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;

-- Grant permissions on tables
GRANT SELECT ON specialized_service_categories TO authenticated, anon;
GRANT SELECT ON specialized_services TO authenticated, anon;
GRANT ALL ON user_preferences TO authenticated;
GRANT ALL ON user_addresses TO authenticated;
GRANT ALL ON specialized_service_orders TO authenticated;
GRANT SELECT ON service_partners TO authenticated, anon;

-- Grant permissions on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- =====================================================
-- REALTIME SUBSCRIPTIONS
-- =====================================================

-- Enable realtime for dynamic updates
ALTER PUBLICATION supabase_realtime ADD TABLE specialized_service_categories;
ALTER PUBLICATION supabase_realtime ADD TABLE specialized_services;
ALTER PUBLICATION supabase_realtime ADD TABLE specialized_service_orders;
ALTER PUBLICATION supabase_realtime ADD TABLE service_partners;
