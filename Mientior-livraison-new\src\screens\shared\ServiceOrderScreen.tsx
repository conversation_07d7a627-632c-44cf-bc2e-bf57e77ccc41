import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

import { colors, typography, spacing } from '../../constants/theme';
import { SpecializedService, SpecializedServiceCategory } from '../../types/specializedServices';
import { CustomTextInput } from '../../components/CustomTextInput';
import { CustomButton } from '../../components/CustomButton';
import SmartAddressInput from '../../components/SmartAddressInput';
import { useAuth } from '../../hooks/useAuth';
import { specializedServicesManager, OrderItem } from '../../services/specializedServicesManager';
import {
  addressAutocompleteService,
  AddressDetails
} from '../../services/addressAutocompleteService';
import {
  distanceCalculationService,
  DistanceCalculationResult
} from '../../services/distanceCalculationService';
import {
  dynamicPriceCalculator,
  PriceEstimate
} from '../../services/dynamicPriceCalculator';
import {
  smartFormAutocompletion,
  FormAutocompletionData,
  UserAddress
} from '../../services/smartFormAutocompletion';

interface ServiceOrderForm {
  items: OrderItem[];
  pickupAddress?: AddressDetails;
  deliveryAddress?: AddressDetails;
  pickupAddressText: string;
  deliveryAddressText: string;
  scheduledTime?: string;
  specialInstructions: string;
  urgentDelivery: boolean;
  selectedOptions: string[];
  contactPhone: string;
  alternativeContact?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  itemValue?: number;
}

const ServiceOrderScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  
  // @ts-ignore
  const { service, category }: { service: SpecializedService; category: SpecializedServiceCategory } = route.params;

  const [formData, setFormData] = useState<ServiceOrderForm>({
    items: [
      {
        id: '1',
        name: service.name,
        description: service.description,
        quantity: 1,
        unitPrice: service.pricing.basePrice,
        totalPrice: service.pricing.basePrice,
      }
    ],
    pickupAddressText: '',
    deliveryAddressText: '',
    specialInstructions: '',
    urgentDelivery: false,
    selectedOptions: [],
    contactPhone: user?.phone || '',
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<ServiceOrderForm>>({});
  const [priceEstimate, setPriceEstimate] = useState<PriceEstimate | null>(null);
  const [distance, setDistance] = useState<DistanceCalculationResult | null>(null);
  const [autocompletionData, setAutocompletionData] = useState<FormAutocompletionData | null>(null);
  const [savedAddresses, setSavedAddresses] = useState<UserAddress[]>([]);
  const [calculatingPrice, setCalculatingPrice] = useState(false);

  useEffect(() => {
    initializeForm();
  }, []);

  useEffect(() => {
    calculatePriceAndDistance();
  }, [
    formData.urgentDelivery,
    formData.selectedOptions,
    formData.items,
    formData.pickupAddress,
    formData.deliveryAddress,
    formData.weight,
    formData.dimensions,
    formData.itemValue
  ]);

  /**
   * Initialiser le formulaire avec les données d'auto-complétion
   */
  const initializeForm = async () => {
    try {
      if (!user?.id) return;

      // Initialiser les données d'auto-complétion
      await smartFormAutocompletion.initializeUserData(user.id);

      // Obtenir les données d'auto-complétion
      const data = await smartFormAutocompletion.getFormAutocompletionData(
        service.id,
        category.id
      );

      setAutocompletionData(data);
      setSavedAddresses(data.suggestedAddresses);

      // Pré-remplir le formulaire
      if (data.prefillData) {
        const updates: Partial<ServiceOrderForm> = {};

        if (data.prefillData.deliveryAddress) {
          updates.deliveryAddress = data.prefillData.deliveryAddress;
          updates.deliveryAddressText = data.prefillData.deliveryAddress.formattedAddress;
        }

        if (data.prefillData.pickupAddress) {
          updates.pickupAddress = data.prefillData.pickupAddress;
          updates.pickupAddressText = data.prefillData.pickupAddress.formattedAddress;
        }

        if (data.prefillData.contactPhone) {
          updates.contactPhone = data.prefillData.contactPhone;
        }

        if (data.prefillData.alternativeContact) {
          updates.alternativeContact = data.prefillData.alternativeContact;
        }

        if (data.prefillData.commonInstructions) {
          updates.specialInstructions = data.prefillData.commonInstructions;
        }

        // Pré-sélectionner les options fréquentes
        if (data.suggestedOptions.length > 0) {
          updates.selectedOptions = data.suggestedOptions.slice(0, 2); // Limiter à 2 options
        }

        setFormData(prev => ({ ...prev, ...updates }));
      }

      console.log('✅ Formulaire initialisé avec auto-complétion');
    } catch (error) {
      console.error('❌ Erreur initialisation formulaire:', error);
    }
  };

  /**
   * Calculer le prix et la distance en temps réel
   */
  const calculatePriceAndDistance = async () => {
    try {
      if (!formData.pickupAddress && !formData.deliveryAddress) {
        return;
      }

      setCalculatingPrice(true);

      // Calculer la distance si les deux adresses sont disponibles
      let distanceResult: DistanceCalculationResult | null = null;

      if (formData.pickupAddress && formData.deliveryAddress) {
        distanceResult = await distanceCalculationService.calculateDistance(
          formData.pickupAddress,
          formData.deliveryAddress,
          { mode: 'driving', trafficModel: 'best_guess' }
        );
        setDistance(distanceResult);
      } else if (formData.deliveryAddress) {
        // Utiliser une adresse de référence (centre-ville d'Abidjan par exemple)
        const referenceLocation = { latitude: 5.3600, longitude: -4.0083 };
        distanceResult = await distanceCalculationService.calculateDistance(
          referenceLocation,
          formData.deliveryAddress,
          { mode: 'driving' }
        );
        setDistance(distanceResult);
      }

      // Calculer le prix dynamique
      const priceInput = {
        service,
        distance: distanceResult || undefined,
        weight: formData.weight,
        dimensions: formData.dimensions,
        itemValue: formData.itemValue,
        selectedOptions: formData.selectedOptions,
        urgentDelivery: formData.urgentDelivery,
        pickupRequired: !!formData.pickupAddress,
      };

      const estimate = await dynamicPriceCalculator.calculatePrice(priceInput);
      setPriceEstimate(estimate);

      console.log('✅ Prix et distance calculés');
    } catch (error) {
      console.error('❌ Erreur calcul prix/distance:', error);
    } finally {
      setCalculatingPrice(false);
    }
  };

  /**
   * Gestionnaire de sélection d'adresse de livraison
   */
  const handleDeliveryAddressSelected = async (address: AddressDetails) => {
    setFormData(prev => ({ ...prev, deliveryAddress: address }));

    // Enregistrer l'utilisation de l'adresse
    if (user?.id) {
      const existingAddress = savedAddresses.find(
        addr => addr.addressDetails.placeId === address.placeId
      );

      if (existingAddress) {
        await smartFormAutocompletion.recordAddressUsage(user.id, existingAddress.id);
      }
    }
  };

  /**
   * Gestionnaire de sélection d'adresse de collecte
   */
  const handlePickupAddressSelected = async (address: AddressDetails) => {
    setFormData(prev => ({ ...prev, pickupAddress: address }));
  };

  /**
   * Gestionnaire de sauvegarde d'adresse
   */
  const handleSaveAddress = async (address: AddressDetails, label: string) => {
    try {
      if (!user?.id) return;

      const savedAddress = await smartFormAutocompletion.saveUserAddress(
        user.id,
        address,
        label,
        savedAddresses.length === 0 // Première adresse = par défaut
      );

      setSavedAddresses(prev => [...prev, savedAddress]);

      Alert.alert('Succès', 'Adresse sauvegardée avec succès');
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de sauvegarder l\'adresse');
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ServiceOrderForm> = {};

    if (!formData.deliveryAddress) {
      newErrors.deliveryAddressText = 'Adresse de livraison requise';
    }

    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = 'Numéro de téléphone requis';
    }

    if (service.partnerIntegration.partnerType === 'dry_cleaner' && !formData.pickupAddress) {
      newErrors.pickupAddressText = 'Adresse de collecte requise pour ce service';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const orderData = {
        userId: user?.id || '',
        serviceId: service.id,
        categoryId: category.id,
        orderDetails: {
          items: formData.items,
          specialInstructions: formData.specialInstructions,
          pickupAddress: formData.pickupAddress?.formattedAddress,
          deliveryAddress: formData.deliveryAddress?.formattedAddress || '',
          scheduledTime: formData.scheduledTime,
          urgentDelivery: formData.urgentDelivery,
        },
        pricing: priceEstimate?.breakdown || {
          basePrice: service.pricing.basePrice,
          additionalFees: {},
          totalPrice: service.pricing.basePrice,
          currency: service.pricing.currency,
        },
        trackingInfo: {
          statusHistory: [],
        },
        paymentInfo: {
          method: 'cash' as const,
          status: 'pending' as const,
        },
      };

      const order = await specializedServicesManager.createServiceOrder(orderData);

      // Enregistrer l'utilisation du service pour l'auto-complétion future
      if (user?.id && priceEstimate) {
        await smartFormAutocompletion.recordServiceUsage(
          user.id,
          service.id,
          category.id,
          formData.selectedOptions,
          priceEstimate.breakdown.finalTotal
        );
      }

      Alert.alert(
        'Commande créée',
        'Votre commande a été créée avec succès. Vous recevrez une confirmation sous peu.',
        [
          {
            text: 'Voir ma commande',
            onPress: () => navigation.navigate('OrderTracking' as never, { orderId: order.id }),
          },
          {
            text: 'Retour',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de créer la commande. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: keyof ServiceOrderForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  /**
   * Obtenir le label d'une option
   */
  const getOptionLabel = (option: string): string => {
    const optionLabels: { [key: string]: string } = {
      'fragile_handling': 'Manipulation fragile',
      'express_delivery': 'Livraison express',
      'signature_required': 'Signature requise',
      'photo_proof': 'Preuve photo',
      'insurance_coverage': 'Couverture assurance',
      'temperature_control': 'Contrôle température',
      'secure_transport': 'Transport sécurisé',
      'special_packaging': 'Emballage spécial',
      'real_time_tracking': 'Suivi temps réel',
      'custom_instructions': 'Instructions personnalisées',
    };

    return optionLabels[option] || option;
  };

  const toggleOption = (option: string) => {
    const newOptions = formData.selectedOptions.includes(option)
      ? formData.selectedOptions.filter(o => o !== option)
      : [...formData.selectedOptions, option];
    updateFormData('selectedOptions', newOptions);
  };

  const renderServiceInfo = () => (
    <View style={styles.serviceInfoCard}>
      <View style={styles.serviceHeader}>
        <View style={[styles.serviceIcon, { backgroundColor: category.color + '20' }]}>
          <Ionicons name={service.icon as any} size={24} color={category.color} />
        </View>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceName}>{service.name}</Text>
          <Text style={styles.serviceDescription}>{service.description}</Text>
        </View>
      </View>
      
      <View style={styles.pricingInfo}>
        <Text style={styles.priceLabel}>Prix estimé</Text>
        <Text style={styles.priceAmount}>
          {priceEstimate ?
            `${priceEstimate.breakdown.finalTotal.toLocaleString()} ${priceEstimate.breakdown.currency}` :
            `${service.pricing.basePrice.toLocaleString()} ${service.pricing.currency}`
          }
        </Text>
        {calculatingPrice && (
          <Text style={styles.calculatingText}>Calcul en cours...</Text>
        )}
        {distance && (
          <Text style={styles.distanceText}>
            Distance: {distanceCalculationService.formatDistance(distance.distance.value)}
            ({distanceCalculationService.formatDuration(distance.duration.value)})
          </Text>
        )}
      </View>
    </View>
  );

  const renderAdditionalOptions = () => {
    const options = Object.keys(service.pricing.additionalFees);
    if (options.length === 0) return null;

    return (
      <View style={styles.optionsSection}>
        <Text style={styles.sectionTitle}>Options supplémentaires</Text>
        {options.map((option) => (
          <TouchableOpacity
            key={option}
            style={[
              styles.optionItem,
              formData.selectedOptions.includes(option) && styles.optionItemSelected,
            ]}
            onPress={() => toggleOption(option)}
          >
            <View style={styles.optionInfo}>
              <Text style={styles.optionName}>{getOptionLabel(option)}</Text>
              <Text style={styles.optionPrice}>
                +{service.pricing.additionalFees[option]} XOF
              </Text>
            </View>
            <Ionicons
              name={formData.selectedOptions.includes(option) ? 'checkmark-circle' : 'ellipse-outline'}
              size={24}
              color={formData.selectedOptions.includes(option) ? colors.primary[500] : colors.neutral[400]}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Commander le Service</Text>
          <Text style={styles.headerSubtitle}>{category.name}</Text>
        </View>
      </View>

      <KeyboardAvoidingView 
        style={styles.flex} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Service Info */}
          {renderServiceInfo()}

          {/* Form */}
          <View style={styles.form}>
            <Text style={styles.formTitle}>Informations de livraison</Text>

            {/* Pickup Address (for certain services) */}
            {(service.partnerIntegration.partnerType === 'dry_cleaner' ||
              service.partnerIntegration.partnerType === 'beauty_salon') && (
              <SmartAddressInput
                label="Adresse de collecte"
                value={formData.pickupAddressText}
                onChangeText={(text) => updateFormData('pickupAddressText', text)}
                onAddressSelected={handlePickupAddressSelected}
                error={errors.pickupAddressText}
                placeholder="Adresse où récupérer les articles"
                savedAddresses={savedAddresses}
                onSaveAddress={handleSaveAddress}
                required
              />
            )}

            <SmartAddressInput
              label="Adresse de livraison"
              value={formData.deliveryAddressText}
              onChangeText={(text) => updateFormData('deliveryAddressText', text)}
              onAddressSelected={handleDeliveryAddressSelected}
              error={errors.deliveryAddressText}
              placeholder="Adresse de livraison complète"
              savedAddresses={savedAddresses}
              onSaveAddress={handleSaveAddress}
              required
            />

            <CustomTextInput
              label="Téléphone de contact"
              value={formData.contactPhone}
              onChangeText={(text) => updateFormData('contactPhone', text)}
              error={errors.contactPhone}
              placeholder="+225 XX XX XX XX XX"
              keyboardType="phone-pad"
              required
            />

            <CustomTextInput
              label="Contact alternatif (optionnel)"
              value={formData.alternativeContact || ''}
              onChangeText={(text) => updateFormData('alternativeContact', text)}
              placeholder="Numéro de téléphone alternatif"
              keyboardType="phone-pad"
            />

            <CustomTextInput
              label="Instructions spéciales"
              value={formData.specialInstructions}
              onChangeText={(text) => updateFormData('specialInstructions', text)}
              placeholder="Instructions particulières pour le service..."
              multiline
              numberOfLines={4}
            />

            {/* Urgent Delivery Toggle */}
            {service.availability.emergencyAvailable && (
              <TouchableOpacity
                style={[
                  styles.urgentToggle,
                  formData.urgentDelivery && styles.urgentToggleActive,
                ]}
                onPress={() => updateFormData('urgentDelivery', !formData.urgentDelivery)}
              >
                <View style={styles.urgentToggleInfo}>
                  <Text style={[
                    styles.urgentToggleText,
                    formData.urgentDelivery && styles.urgentToggleTextActive,
                  ]}>
                    Livraison urgente
                  </Text>
                  <Text style={styles.urgentToggleSubtext}>
                    Livraison prioritaire en moins de {service.availability.minDeliveryTime} minutes
                  </Text>
                </View>
                <Ionicons
                  name={formData.urgentDelivery ? 'checkmark-circle' : 'ellipse-outline'}
                  size={24}
                  color={formData.urgentDelivery ? colors.primary[500] : colors.neutral[400]}
                />
              </TouchableOpacity>
            )}
          </View>

          {/* Additional Options */}
          {renderAdditionalOptions()}

          {/* Service Requirements */}
          {service.requirements.length > 0 && (
            <View style={styles.requirementsCard}>
              <Text style={styles.requirementsTitle}>Exigences du service</Text>
              {service.requirements.map((requirement, index) => (
                <View key={index} style={styles.requirementItem}>
                  <Ionicons name="information-circle" size={16} color={colors.warning} />
                  <Text style={styles.requirementText}>{requirement}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Price Breakdown */}
          {priceEstimate && (
            <View style={styles.priceBreakdownCard}>
              <Text style={styles.priceBreakdownTitle}>Détail du prix</Text>

              <View style={styles.priceBreakdownItem}>
                <Text style={styles.priceBreakdownLabel}>Prix de base</Text>
                <Text style={styles.priceBreakdownValue}>
                  {priceEstimate.breakdown.basePrice.toLocaleString()} {priceEstimate.breakdown.currency}
                </Text>
              </View>

              {priceEstimate.breakdown.distanceFee > 0 && (
                <View style={styles.priceBreakdownItem}>
                  <Text style={styles.priceBreakdownLabel}>Frais de distance</Text>
                  <Text style={styles.priceBreakdownValue}>
                    {priceEstimate.breakdown.distanceFee.toLocaleString()} {priceEstimate.breakdown.currency}
                  </Text>
                </View>
              )}

              {Object.entries(priceEstimate.breakdown.additionalOptions).map(([option, fee]) => (
                <View key={option} style={styles.priceBreakdownItem}>
                  <Text style={styles.priceBreakdownLabel}>{getOptionLabel(option)}</Text>
                  <Text style={styles.priceBreakdownValue}>
                    {fee.toLocaleString()} {priceEstimate.breakdown.currency}
                  </Text>
                </View>
              ))}

              {priceEstimate.breakdown.totalDiscounts > 0 && (
                <View style={styles.priceBreakdownItem}>
                  <Text style={[styles.priceBreakdownLabel, { color: colors.success }]}>Remises</Text>
                  <Text style={[styles.priceBreakdownValue, { color: colors.success }]}>
                    -{priceEstimate.breakdown.totalDiscounts.toLocaleString()} {priceEstimate.breakdown.currency}
                  </Text>
                </View>
              )}

              <View style={[styles.priceBreakdownItem, styles.priceBreakdownTotal]}>
                <Text style={styles.priceBreakdownTotalLabel}>Total</Text>
                <Text style={styles.priceBreakdownTotalValue}>
                  {priceEstimate.breakdown.finalTotal.toLocaleString()} {priceEstimate.breakdown.currency}
                </Text>
              </View>

              {priceEstimate.recommendations && priceEstimate.recommendations.length > 0 && (
                <View style={styles.recommendationsSection}>
                  <Text style={styles.recommendationsTitle}>💡 Recommandations</Text>
                  {priceEstimate.recommendations.map((recommendation, index) => (
                    <Text key={index} style={styles.recommendationText}>
                      • {recommendation}
                    </Text>
                  ))}
                </View>
              )}
            </View>
          )}

          {/* Submit Button */}
          <View style={styles.submitContainer}>
            <View style={styles.totalPriceContainer}>
              <Text style={styles.totalPriceLabel}>Total estimé</Text>
              <Text style={styles.totalPriceAmount}>
                {priceEstimate ?
                  `${priceEstimate.breakdown.finalTotal.toLocaleString()} ${priceEstimate.breakdown.currency}` :
                  `${service.pricing.basePrice.toLocaleString()} ${service.pricing.currency}`
                }
              </Text>
              {priceEstimate && (
                <Text style={styles.confidenceText}>
                  Confiance: {priceEstimate.confidence === 'high' ? 'Élevée' :
                             priceEstimate.confidence === 'medium' ? 'Moyenne' : 'Faible'}
                </Text>
              )}
            </View>
            
            <CustomButton
              title="Confirmer la commande"
              onPress={handleSubmit}
              loading={loading}
              disabled={loading}
              gradient
              leftIcon="checkmark-circle"
            />
            
            <Text style={styles.submitNote}>
              Vous recevrez une confirmation par SMS une fois la commande validée
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  flex: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
  },
  serviceInfoCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
    padding: spacing.lg,
    marginVertical: spacing.md,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  serviceIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 20,
  },
  pricingInfo: {
    alignItems: 'center',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.neutral[200],
  },
  priceLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  priceAmount: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  form: {
    gap: spacing.md,
  },
  formTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  urgentToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  urgentToggleActive: {
    backgroundColor: colors.primary[50],
    borderColor: colors.primary[300],
  },
  urgentToggleInfo: {
    flex: 1,
  },
  urgentToggleText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: 2,
  },
  urgentToggleTextActive: {
    color: colors.primary[700],
  },
  urgentToggleSubtext: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  optionsSection: {
    marginTop: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  optionItemSelected: {
    backgroundColor: colors.primary[50],
    borderColor: colors.primary[300],
  },
  optionInfo: {
    flex: 1,
  },
  optionName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: 2,
  },
  optionPrice: {
    fontSize: typography.fontSize.sm,
    color: colors.warning,
    fontWeight: typography.fontWeight.medium,
  },
  requirementsCard: {
    backgroundColor: colors.warning + '10',
    borderRadius: 12,
    padding: spacing.md,
    marginTop: spacing.lg,
    borderWidth: 1,
    borderColor: colors.warning + '30',
  },
  requirementsTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.xs,
  },
  requirementText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    flex: 1,
  },
  submitContainer: {
    marginTop: spacing.xl,
    alignItems: 'center',
  },
  totalPriceContainer: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.lg,
    alignItems: 'center',
    marginBottom: spacing.lg,
    width: '100%',
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  totalPriceLabel: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  totalPriceAmount: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  submitNote: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    textAlign: 'center',
    marginTop: spacing.md,
    lineHeight: 20,
  },

  // Styles pour les nouvelles fonctionnalités
  calculatingText: {
    fontSize: typography.fontSize.xs,
    color: colors.warning,
    fontStyle: 'italic',
    marginTop: 2,
  },
  distanceText: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
    marginTop: 2,
  },
  priceBreakdownCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    marginTop: spacing.lg,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  priceBreakdownTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  priceBreakdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.xs,
  },
  priceBreakdownLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  priceBreakdownValue: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
  },
  priceBreakdownTotal: {
    borderTopWidth: 1,
    borderTopColor: colors.neutral[300],
    marginTop: spacing.sm,
    paddingTop: spacing.sm,
  },
  priceBreakdownTotalLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  priceBreakdownTotalValue: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  recommendationsSection: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.neutral[200],
  },
  recommendationsTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  recommendationText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 18,
    marginBottom: spacing.xs,
  },
  confidenceText: {
    fontSize: typography.fontSize.xs,
    color: colors.text.secondary,
    marginTop: 2,
  },
});

export default ServiceOrderScreen;
