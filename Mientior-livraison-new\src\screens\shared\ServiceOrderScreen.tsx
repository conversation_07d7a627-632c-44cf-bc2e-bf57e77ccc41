import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

import { colors, typography, spacing } from '../../constants/theme';
import { SpecializedService, SpecializedServiceCategory } from '../../types/specializedServices';
import { CustomTextInput } from '../../components/CustomTextInput';
import { CustomButton } from '../../components/CustomButton';
import { useAuth } from '../../hooks/useAuth';
import { specializedServicesManager, OrderItem } from '../../services/specializedServicesManager';

interface ServiceOrderForm {
  items: OrderItem[];
  pickupAddress?: string;
  deliveryAddress: string;
  scheduledTime?: string;
  specialInstructions: string;
  urgentDelivery: boolean;
  selectedOptions: string[];
  contactPhone: string;
  alternativeContact?: string;
}

const ServiceOrderScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  
  // @ts-ignore
  const { service, category }: { service: SpecializedService; category: SpecializedServiceCategory } = route.params;

  const [formData, setFormData] = useState<ServiceOrderForm>({
    items: [
      {
        id: '1',
        name: service.name,
        description: service.description,
        quantity: 1,
        unitPrice: service.pricing.basePrice,
        totalPrice: service.pricing.basePrice,
      }
    ],
    deliveryAddress: '',
    specialInstructions: '',
    urgentDelivery: false,
    selectedOptions: [],
    contactPhone: user?.phone || '',
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<ServiceOrderForm>>({});
  const [estimatedPrice, setEstimatedPrice] = useState(service.pricing.basePrice);

  useEffect(() => {
    calculatePrice();
  }, [formData.urgentDelivery, formData.selectedOptions, formData.items]);

  const calculatePrice = () => {
    const pricing = specializedServicesManager.calculateServicePrice(service, {
      items: formData.items,
      urgentDelivery: formData.urgentDelivery,
      specialRequirements: formData.selectedOptions,
    });
    setEstimatedPrice(pricing.totalPrice);
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ServiceOrderForm> = {};

    if (!formData.deliveryAddress.trim()) {
      newErrors.deliveryAddress = 'Adresse de livraison requise';
    }

    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = 'Numéro de téléphone requis';
    }

    if (service.partnerIntegration.partnerType === 'dry_cleaner' && !formData.pickupAddress?.trim()) {
      newErrors.pickupAddress = 'Adresse de collecte requise pour ce service';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const orderData = {
        userId: user?.id || '',
        serviceId: service.id,
        categoryId: category.id,
        orderDetails: {
          items: formData.items,
          specialInstructions: formData.specialInstructions,
          pickupAddress: formData.pickupAddress,
          deliveryAddress: formData.deliveryAddress,
          scheduledTime: formData.scheduledTime,
          urgentDelivery: formData.urgentDelivery,
        },
        pricing: specializedServicesManager.calculateServicePrice(service, {
          items: formData.items,
          urgentDelivery: formData.urgentDelivery,
          specialRequirements: formData.selectedOptions,
        }),
        trackingInfo: {
          statusHistory: [],
        },
        paymentInfo: {
          method: 'cash' as const,
          status: 'pending' as const,
        },
      };

      const order = await specializedServicesManager.createServiceOrder(orderData);

      Alert.alert(
        'Commande créée',
        'Votre commande a été créée avec succès. Vous recevrez une confirmation sous peu.',
        [
          {
            text: 'Voir ma commande',
            onPress: () => navigation.navigate('OrderTracking' as never, { orderId: order.id }),
          },
          {
            text: 'Retour',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de créer la commande. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: keyof ServiceOrderForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const toggleOption = (option: string) => {
    const newOptions = formData.selectedOptions.includes(option)
      ? formData.selectedOptions.filter(o => o !== option)
      : [...formData.selectedOptions, option];
    updateFormData('selectedOptions', newOptions);
  };

  const renderServiceInfo = () => (
    <View style={styles.serviceInfoCard}>
      <View style={styles.serviceHeader}>
        <View style={[styles.serviceIcon, { backgroundColor: category.color + '20' }]}>
          <Ionicons name={service.icon as any} size={24} color={category.color} />
        </View>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceName}>{service.name}</Text>
          <Text style={styles.serviceDescription}>{service.description}</Text>
        </View>
      </View>
      
      <View style={styles.pricingInfo}>
        <Text style={styles.priceLabel}>Prix estimé</Text>
        <Text style={styles.priceAmount}>
          {estimatedPrice.toLocaleString()} {service.pricing.currency}
        </Text>
      </View>
    </View>
  );

  const renderAdditionalOptions = () => {
    const options = Object.keys(service.pricing.additionalFees);
    if (options.length === 0) return null;

    return (
      <View style={styles.optionsSection}>
        <Text style={styles.sectionTitle}>Options supplémentaires</Text>
        {options.map((option) => (
          <TouchableOpacity
            key={option}
            style={[
              styles.optionItem,
              formData.selectedOptions.includes(option) && styles.optionItemSelected,
            ]}
            onPress={() => toggleOption(option)}
          >
            <View style={styles.optionInfo}>
              <Text style={styles.optionName}>{getOptionLabel(option)}</Text>
              <Text style={styles.optionPrice}>
                +{service.pricing.additionalFees[option]} XOF
              </Text>
            </View>
            <Ionicons
              name={formData.selectedOptions.includes(option) ? 'checkmark-circle' : 'ellipse-outline'}
              size={24}
              color={formData.selectedOptions.includes(option) ? colors.primary[500] : colors.neutral[400]}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const getOptionLabel = (option: string): string => {
    const labels: Record<string, string> = {
      expressDelivery: 'Livraison express',
      fragileHandling: 'Manipulation fragile',
      insurance: 'Assurance tous risques',
      temperatureControl: 'Contrôle de température',
      specialPackaging: 'Emballage spécialisé',
      emergencyService: 'Service d\'urgence',
      pickupService: 'Service de collecte',
      customization: 'Personnalisation',
    };
    return labels[option] || option;
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Commander le Service</Text>
          <Text style={styles.headerSubtitle}>{category.name}</Text>
        </View>
      </View>

      <KeyboardAvoidingView 
        style={styles.flex} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Service Info */}
          {renderServiceInfo()}

          {/* Form */}
          <View style={styles.form}>
            <Text style={styles.formTitle}>Informations de livraison</Text>

            {/* Pickup Address (for certain services) */}
            {(service.partnerIntegration.partnerType === 'dry_cleaner' || 
              service.partnerIntegration.partnerType === 'beauty_salon') && (
              <CustomTextInput
                label="Adresse de collecte"
                value={formData.pickupAddress || ''}
                onChangeText={(text) => updateFormData('pickupAddress', text)}
                error={errors.pickupAddress}
                placeholder="Adresse où récupérer les articles"
                multiline
                numberOfLines={2}
                required
              />
            )}

            <CustomTextInput
              label="Adresse de livraison"
              value={formData.deliveryAddress}
              onChangeText={(text) => updateFormData('deliveryAddress', text)}
              error={errors.deliveryAddress}
              placeholder="Adresse de livraison complète"
              multiline
              numberOfLines={2}
              required
            />

            <CustomTextInput
              label="Téléphone de contact"
              value={formData.contactPhone}
              onChangeText={(text) => updateFormData('contactPhone', text)}
              error={errors.contactPhone}
              placeholder="+225 XX XX XX XX XX"
              keyboardType="phone-pad"
              required
            />

            <CustomTextInput
              label="Contact alternatif (optionnel)"
              value={formData.alternativeContact || ''}
              onChangeText={(text) => updateFormData('alternativeContact', text)}
              placeholder="Numéro de téléphone alternatif"
              keyboardType="phone-pad"
            />

            <CustomTextInput
              label="Instructions spéciales"
              value={formData.specialInstructions}
              onChangeText={(text) => updateFormData('specialInstructions', text)}
              placeholder="Instructions particulières pour le service..."
              multiline
              numberOfLines={4}
            />

            {/* Urgent Delivery Toggle */}
            {service.availability.emergencyAvailable && (
              <TouchableOpacity
                style={[
                  styles.urgentToggle,
                  formData.urgentDelivery && styles.urgentToggleActive,
                ]}
                onPress={() => updateFormData('urgentDelivery', !formData.urgentDelivery)}
              >
                <View style={styles.urgentToggleInfo}>
                  <Text style={[
                    styles.urgentToggleText,
                    formData.urgentDelivery && styles.urgentToggleTextActive,
                  ]}>
                    Livraison urgente
                  </Text>
                  <Text style={styles.urgentToggleSubtext}>
                    Livraison prioritaire en moins de {service.availability.minDeliveryTime} minutes
                  </Text>
                </View>
                <Ionicons
                  name={formData.urgentDelivery ? 'checkmark-circle' : 'ellipse-outline'}
                  size={24}
                  color={formData.urgentDelivery ? colors.primary[500] : colors.neutral[400]}
                />
              </TouchableOpacity>
            )}
          </View>

          {/* Additional Options */}
          {renderAdditionalOptions()}

          {/* Service Requirements */}
          {service.requirements.length > 0 && (
            <View style={styles.requirementsCard}>
              <Text style={styles.requirementsTitle}>Exigences du service</Text>
              {service.requirements.map((requirement, index) => (
                <View key={index} style={styles.requirementItem}>
                  <Ionicons name="information-circle" size={16} color={colors.warning} />
                  <Text style={styles.requirementText}>{requirement}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Submit Button */}
          <View style={styles.submitContainer}>
            <View style={styles.totalPriceContainer}>
              <Text style={styles.totalPriceLabel}>Total estimé</Text>
              <Text style={styles.totalPriceAmount}>
                {estimatedPrice.toLocaleString()} {service.pricing.currency}
              </Text>
            </View>
            
            <CustomButton
              title="Confirmer la commande"
              onPress={handleSubmit}
              loading={loading}
              disabled={loading}
              gradient
              leftIcon="checkmark-circle"
            />
            
            <Text style={styles.submitNote}>
              Vous recevrez une confirmation par SMS une fois la commande validée
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  flex: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral[200],
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl,
  },
  serviceInfoCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
    padding: spacing.lg,
    marginVertical: spacing.md,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  serviceIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    lineHeight: 20,
  },
  pricingInfo: {
    alignItems: 'center',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.neutral[200],
  },
  priceLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  priceAmount: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  form: {
    gap: spacing.md,
  },
  formTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  urgentToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  urgentToggleActive: {
    backgroundColor: colors.primary[50],
    borderColor: colors.primary[300],
  },
  urgentToggleInfo: {
    flex: 1,
  },
  urgentToggleText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: 2,
  },
  urgentToggleTextActive: {
    color: colors.primary[700],
  },
  urgentToggleSubtext: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  optionsSection: {
    marginTop: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  optionItemSelected: {
    backgroundColor: colors.primary[50],
    borderColor: colors.primary[300],
  },
  optionInfo: {
    flex: 1,
  },
  optionName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    marginBottom: 2,
  },
  optionPrice: {
    fontSize: typography.fontSize.sm,
    color: colors.warning,
    fontWeight: typography.fontWeight.medium,
  },
  requirementsCard: {
    backgroundColor: colors.warning + '10',
    borderRadius: 12,
    padding: spacing.md,
    marginTop: spacing.lg,
    borderWidth: 1,
    borderColor: colors.warning + '30',
  },
  requirementsTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.xs,
  },
  requirementText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    flex: 1,
  },
  submitContainer: {
    marginTop: spacing.xl,
    alignItems: 'center',
  },
  totalPriceContainer: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.lg,
    alignItems: 'center',
    marginBottom: spacing.lg,
    width: '100%',
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  totalPriceLabel: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  totalPriceAmount: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  submitNote: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    textAlign: 'center',
    marginTop: spacing.md,
    lineHeight: 20,
  },
});

export default ServiceOrderScreen;
