// Service de validation de la configuration et des dépendances
import { getApiKeys, areApiKeysConfigured } from '../config/apiKeys';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  recommendations: string[];
}

export interface ServiceStatus {
  name: string;
  status: 'configured' | 'missing' | 'error';
  message: string;
}

class ConfigurationValidator {
  /**
   * Valider la configuration complète
   */
  async validateConfiguration(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];

    try {
      // Valider les clés API
      const apiValidation = this.validateApiKeys();
      errors.push(...apiValidation.errors);
      warnings.push(...apiValidation.warnings);
      recommendations.push(...apiValidation.recommendations);

      // Valider les dépendances
      const dependenciesValidation = this.validateDependencies();
      errors.push(...dependenciesValidation.errors);
      warnings.push(...dependenciesValidation.warnings);

      // Valider la configuration Supabase
      const supabaseValidation = this.validateSupabaseConfig();
      errors.push(...supabaseValidation.errors);
      warnings.push(...supabaseValidation.warnings);

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        recommendations,
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Erreur lors de la validation: ${error}`],
        warnings,
        recommendations,
      };
    }
  }

  /**
   * Valider les clés API Google
   */
  private validateApiKeys(): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];

    try {
      const apiKeys = getApiKeys();
      const isConfigured = areApiKeysConfigured();

      if (!isConfigured) {
        warnings.push('Les clés API Google ne sont pas configurées');
        recommendations.push('Configurez vos clés API Google dans les variables d\'environnement');
        recommendations.push('Visitez https://console.cloud.google.com/ pour obtenir vos clés');
      }

      // Vérifier chaque clé individuellement
      if (apiKeys.googlePlaces.startsWith('YOUR_')) {
        warnings.push('Google Places API key non configurée');
        recommendations.push('Définissez EXPO_PUBLIC_GOOGLE_PLACES_API_KEY');
      }

      if (apiKeys.googleMaps.startsWith('YOUR_')) {
        warnings.push('Google Maps API key non configurée');
        recommendations.push('Définissez EXPO_PUBLIC_GOOGLE_MAPS_API_KEY');
      }

      if (apiKeys.googleDistanceMatrix.startsWith('YOUR_')) {
        warnings.push('Google Distance Matrix API key non configurée');
        recommendations.push('Définissez EXPO_PUBLIC_GOOGLE_DISTANCE_MATRIX_API_KEY');
      }

      return { isValid: errors.length === 0, errors, warnings, recommendations };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Erreur validation clés API: ${error}`],
        warnings,
        recommendations,
      };
    }
  }

  /**
   * Valider les dépendances npm
   */
  private validateDependencies(): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Vérifier les dépendances critiques
      const criticalDependencies = [
        '@react-native-async-storage/async-storage',
        'use-debounce',
        '@expo/vector-icons',
        '@react-navigation/native',
      ];

      criticalDependencies.forEach(dep => {
        try {
          require.resolve(dep);
        } catch (error) {
          errors.push(`Dépendance manquante: ${dep}`);
        }
      });

      return { isValid: errors.length === 0, errors, warnings, recommendations: [] };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Erreur validation dépendances: ${error}`],
        warnings,
        recommendations: [],
      };
    }
  }

  /**
   * Valider la configuration Supabase
   */
  private validateSupabaseConfig(): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

      if (!supabaseUrl || supabaseUrl.includes('your-project')) {
        warnings.push('URL Supabase non configurée');
      }

      if (!supabaseKey || supabaseKey.includes('your_anon_key')) {
        warnings.push('Clé Supabase non configurée');
      }

      return { isValid: errors.length === 0, errors, warnings, recommendations: [] };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Erreur validation Supabase: ${error}`],
        warnings,
        recommendations: [],
      };
    }
  }

  /**
   * Obtenir le statut de tous les services
   */
  async getServicesStatus(): Promise<ServiceStatus[]> {
    const services: ServiceStatus[] = [];

    // Statut des services Google
    const apiKeys = getApiKeys();
    
    services.push({
      name: 'Google Places API',
      status: apiKeys.googlePlaces.startsWith('YOUR_') ? 'missing' : 'configured',
      message: apiKeys.googlePlaces.startsWith('YOUR_') 
        ? 'Clé API non configurée' 
        : 'Service configuré et prêt',
    });

    services.push({
      name: 'Google Distance Matrix API',
      status: apiKeys.googleDistanceMatrix.startsWith('YOUR_') ? 'missing' : 'configured',
      message: apiKeys.googleDistanceMatrix.startsWith('YOUR_') 
        ? 'Clé API non configurée' 
        : 'Service configuré et prêt',
    });

    // Statut Supabase
    const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
    services.push({
      name: 'Supabase Database',
      status: (!supabaseUrl || supabaseUrl.includes('your-project')) ? 'missing' : 'configured',
      message: (!supabaseUrl || supabaseUrl.includes('your-project'))
        ? 'Configuration Supabase manquante'
        : 'Base de données configurée',
    });

    // Statut des services dynamiques
    services.push({
      name: 'Services Dynamiques',
      status: 'configured',
      message: 'Chargement dynamique des services implémenté',
    });

    services.push({
      name: 'Auto-complétion Intelligente',
      status: 'configured',
      message: 'Système d\'auto-complétion opérationnel',
    });

    return services;
  }

  /**
   * Tester la connectivité des services
   */
  async testServicesConnectivity(): Promise<{ [serviceName: string]: boolean }> {
    const results: { [serviceName: string]: boolean } = {};

    // Test Google Places (si configuré)
    try {
      const apiKeys = getApiKeys();
      if (!apiKeys.googlePlaces.startsWith('YOUR_')) {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=test&key=${apiKeys.googlePlaces}`,
          { method: 'GET' }
        );
        results['Google Places'] = response.status !== 403; // 403 = clé invalide
      } else {
        results['Google Places'] = false;
      }
    } catch (error) {
      results['Google Places'] = false;
    }

    // Test Supabase (si configuré)
    try {
      const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
      if (supabaseUrl && !supabaseUrl.includes('your-project')) {
        const response = await fetch(`${supabaseUrl}/rest/v1/`, {
          method: 'HEAD',
        });
        results['Supabase'] = response.ok;
      } else {
        results['Supabase'] = false;
      }
    } catch (error) {
      results['Supabase'] = false;
    }

    return results;
  }

  /**
   * Générer un rapport de configuration
   */
  async generateConfigurationReport(): Promise<string> {
    const validation = await this.validateConfiguration();
    const services = await this.getServicesStatus();
    const connectivity = await this.testServicesConnectivity();

    let report = '📋 RAPPORT DE CONFIGURATION - SERVICES DYNAMIQUES\n\n';

    // Statut général
    report += `🎯 Statut général: ${validation.isValid ? '✅ Valide' : '❌ Problèmes détectés'}\n\n`;

    // Services
    report += '🔧 STATUT DES SERVICES:\n';
    services.forEach(service => {
      const icon = service.status === 'configured' ? '✅' : '⚠️';
      report += `${icon} ${service.name}: ${service.message}\n`;
    });

    // Connectivité
    report += '\n🌐 CONNECTIVITÉ:\n';
    Object.entries(connectivity).forEach(([service, isConnected]) => {
      const icon = isConnected ? '✅' : '❌';
      report += `${icon} ${service}: ${isConnected ? 'Connecté' : 'Non accessible'}\n`;
    });

    // Erreurs
    if (validation.errors.length > 0) {
      report += '\n❌ ERREURS:\n';
      validation.errors.forEach(error => {
        report += `• ${error}\n`;
      });
    }

    // Avertissements
    if (validation.warnings.length > 0) {
      report += '\n⚠️ AVERTISSEMENTS:\n';
      validation.warnings.forEach(warning => {
        report += `• ${warning}\n`;
      });
    }

    // Recommandations
    if (validation.recommendations.length > 0) {
      report += '\n💡 RECOMMANDATIONS:\n';
      validation.recommendations.forEach(recommendation => {
        report += `• ${recommendation}\n`;
      });
    }

    return report;
  }
}

export const configurationValidator = new ConfigurationValidator();
export default configurationValidator;
