import React from 'react';
import {
  TouchableOpacity,
  TouchableOpacityProps,
  Text,
  View,
  ActivityIndicator,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing, borderRadius, shadows } from '../constants/theme';

export interface CustomButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  gradient?: boolean;
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const CustomButton: React.FC<CustomButtonProps> = ({
  title,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  leftIcon,
  rightIcon,
  gradient = false,
  fullWidth = true,
  style,
  textStyle,
  onPress,
  ...props
}) => {
  const isDisabled = disabled || loading;

  const buttonStyles = [
    styles.base,
    styles[size],
    styles[variant],
    fullWidth && styles.fullWidth,
    isDisabled && styles.disabled,
    style,
  ];

  const textStyles = [
    styles.text,
    styles[`text_${size}`],
    styles[`text_${variant}`],
    isDisabled && styles.textDisabled,
    textStyle,
  ];

  const ButtonContent = () => (
    <View style={styles.content}>
      {leftIcon && !loading && (
        <View style={styles.leftIcon}>
          <Ionicons name={leftIcon} size={size === 'sm' ? 16 : size === 'lg' ? 24 : 20} color={
            variant === 'outline' || variant === 'ghost' 
              ? colors.primary[500] 
              : colors.text.inverse
          } />
        </View>
      )}
      
      {loading ? (
        <ActivityIndicator
          size={size === 'sm' ? 'small' : 'small'}
          color={variant === 'outline' || variant === 'ghost' ? colors.primary[500] : colors.text.inverse}
        />
      ) : (
        <Text style={textStyles} numberOfLines={1}>
          {title}
        </Text>
      )}
      
      {rightIcon && !loading && (
        <View style={styles.rightIcon}>
          <Ionicons name={rightIcon} size={size === 'sm' ? 16 : size === 'lg' ? 24 : 20} color={
            variant === 'outline' || variant === 'ghost' 
              ? colors.primary[500] 
              : colors.text.inverse
          } />
        </View>
      )}
    </View>
  );

  if (gradient && variant === 'primary' && !isDisabled) {
    return (
      <TouchableOpacity
        {...props}
        onPress={onPress}
        disabled={isDisabled}
        style={[styles.base, styles[size], fullWidth && styles.fullWidth, style]}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={[colors.primary[400], colors.primary[600]]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.gradient, styles[size]]}
        >
          <ButtonContent />
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      {...props}
      onPress={onPress}
      disabled={isDisabled}
      style={buttonStyles}
      activeOpacity={0.8}
    >
      <ButtonContent />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    ...shadows.sm,
  },
  
  // Tailles
  sm: {
    height: 40,
    paddingHorizontal: spacing.sm,
    minWidth: 80,
  },
  md: {
    height: 48,
    paddingHorizontal: spacing.md,
    minWidth: 100,
  },
  lg: {
    height: 56,
    paddingHorizontal: spacing.lg,
    minWidth: 120,
  },

  // Variantes
  primary: {
    backgroundColor: colors.primary[500],
  },
  secondary: {
    backgroundColor: colors.secondary[500],
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1.5,
    borderColor: colors.primary[500],
  },
  ghost: {
    backgroundColor: 'transparent',
  },
  danger: {
    backgroundColor: colors.error,
  },

  // États
  disabled: {
    backgroundColor: colors.neutral[300],
    borderColor: colors.neutral[300],
  },

  fullWidth: {
    width: '100%',
  },

  gradient: {
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },

  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  leftIcon: {
    marginRight: spacing.xs,
  },

  rightIcon: {
    marginLeft: spacing.xs,
  },

  // Styles de texte
  text: {
    fontWeight: typography.fontWeight.semibold,
    textAlign: 'center',
  },

  text_sm: {
    fontSize: typography.fontSize.sm,
  },
  text_md: {
    fontSize: typography.fontSize.md,
  },
  text_lg: {
    fontSize: typography.fontSize.lg,
  },

  text_primary: {
    color: colors.text.inverse,
  },
  text_secondary: {
    color: colors.text.inverse,
  },
  text_outline: {
    color: colors.primary[500],
  },
  text_ghost: {
    color: colors.primary[500],
  },
  text_danger: {
    color: colors.text.inverse,
  },

  textDisabled: {
    color: colors.text.disabled,
  },
});

export default CustomButton;
