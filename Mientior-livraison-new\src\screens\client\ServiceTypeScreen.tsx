import React, { useCallback, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useQuery } from '@tanstack/react-query';
import { colors } from '../../constants/theme';
import { ServiceTypesService, ServiceType } from '../../services/database';
import { HapticFeedback } from '../../utils/hapticFeedback';
import { ENHANCED_SERVICE_CATEGORIES } from '../../types/enhancedServices';
import { SPECIALIZED_SERVICE_CATEGORIES } from '../../types/specializedServices';

// Mapping des icônes vectorielles pour les types de services
const getServiceIcon = (businessType: string, iconName?: string): string => {
  // Si l'icône est déjà définie dans la base de données, l'utiliser
  if (iconName && !iconName.includes('🎯') && !iconName.includes('🏪') && !iconName.includes('📦')) {
    return iconName;
  }

  // Mapping basé sur business_type pour assurer la cohérence avec HomeScreen
  const iconMap: Record<string, string> = {
    restaurant: 'restaurant',
    delivery: 'car',
    grocery: 'storefront',
    pharmacy: 'medical-outline',
    bakery: 'cafe-outline',
    laundry: 'shirt-outline',
    beauty: 'cut-outline',
    electronics: 'phone-portrait-outline',
    bookstore: 'library-outline',
    // Fallbacks pour d'autres types
    epicerie: 'storefront',
    supermarche: 'storefront',
    boutique: 'storefront',
    coiffure: 'cut-outline',
    pressing: 'shirt-outline',
    boulangerie: 'cafe-outline',
    electronique: 'phone-portrait-outline',
    librairie: 'library-outline',
  };

  return iconMap[businessType] || 'storefront';
};

// Fonction pour obtenir la couleur de fond basée sur la couleur principale
const getBackgroundColor = (color: string): string => {
  const colorMap: Record<string, string> = {
    '#FF6B35': '#FFE8E8',  // Orange -> Orange pâle
    '#4ECDC4': '#E8F8FF',  // Turquoise -> Turquoise pâle
    '#0DCAA8': '#E8FFE8',  // Vert -> Vert pâle
    '#FF6B6B': '#FFE8E8',  // Rouge médical -> Rouge pâle
    '#D4A574': '#F5F0E8',  // Beige doré -> Beige très pâle
    '#9C88FF': '#F0E8FF',  // Violet -> Violet pâle
    '#FFB347': '#FFF8E8',  // Orange doux -> Orange très pâle
    '#32CD32': '#E8F8E8',  // Vert naturel -> Vert naturel pâle
    '#8B4513': '#F0E8E0',  // Marron -> Marron pâle
  };

  return colorMap[color] || '#F0F0F0'; // Couleur par défaut si non trouvée
};

export const ServiceTypeScreen: React.FC = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const serviceTypesService = new ServiceTypesService();

  // Requête pour les types de services
  const {
    data: serviceTypes = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['serviceTypes'],
    queryFn: () => serviceTypesService.getAll(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Fonction de rafraîchissement
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    HapticFeedback.light();
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  // Navigation vers la liste des restaurants filtrée
  const navigateToServiceType = useCallback((serviceType: ServiceType) => {
    HapticFeedback.medium();
    console.log('🏷️ Navigating to service type:', serviceType.title);
    
    // Navigation vers CategoryRestaurants avec le type de service
    (navigation as any).navigate('CategoryRestaurants', {
      categoryId: serviceType.id,
      categoryName: serviceType.title,
      serviceType: serviceType.business_type
    });
  }, [navigation]);

  // Retour à l'écran précédent
  const handleGoBack = useCallback(() => {
    HapticFeedback.light();
    navigation.goBack();
  }, [navigation]);

  // Rendu d'une carte de type de service
  const renderServiceTypeCard = useCallback((serviceType: ServiceType) => {
    const iconName = getServiceIcon(serviceType.business_type, serviceType.icon);
    const backgroundColor = getBackgroundColor(serviceType.color);

    return (
      <TouchableOpacity
        key={serviceType.id}
        style={styles.serviceCard}
        onPress={() => navigateToServiceType(serviceType)}
        activeOpacity={0.8}
        accessible={true}
        accessibilityRole="button"
        accessibilityLabel={`Sélectionner ${serviceType.title}`}
        accessibilityHint={serviceType.description}
      >
        <View style={[styles.serviceIconContainer, { backgroundColor }]}>
          <Ionicons
            name={iconName as any}
            size={24}
            color={serviceType.color}
          />
        </View>

        <View style={styles.serviceInfo}>
          <Text style={styles.serviceTitle}>{serviceType.title}</Text>
          <Text style={styles.serviceDescription}>{serviceType.description}</Text>

          {/* Badge populaire pour certains services */}
          {serviceType.business_type === 'restaurant' && (
            <View style={styles.popularBadge}>
              <Text style={styles.popularBadgeText}>Plus populaire</Text>
            </View>
          )}

          {/* Temps estimé */}
          <View style={styles.timeContainer}>
            <Ionicons name="time-outline" size={14} color={colors.text.secondary} />
            <Text style={styles.timeText}>
              {getEstimatedTime(serviceType.business_type)}
            </Text>
          </View>
        </View>

        <View style={styles.arrowContainer}>
          <Ionicons name="chevron-forward" size={20} color={colors.text.secondary} />
        </View>
      </TouchableOpacity>
    );
  }, [navigateToServiceType]);

  // Fonction pour obtenir le temps estimé selon le type de service
  const getEstimatedTime = (businessType: string): string => {
    const timeMap: Record<string, string> = {
      restaurant: '20-45 min',
      delivery: '15-30 min',
      grocery: '30-60 min',
      pharmacy: '15-30 min',
      bakery: '20-40 min',
      laundry: '2-3 jours',
      beauty: 'Sur RDV',
      electronics: '45-90 min',
      bookstore: '30-60 min',
      // Fallbacks pour anciens noms
      epicerie: '30-60 min',
      pharmacie: '15-30 min',
      boulangerie: '20-40 min',
      pressing: '2-3 jours',
      coiffure: 'Sur RDV',
      electronique: '45-90 min',
      librairie: '30-60 min',
      supermarche: '45-90 min',
      boutique: '30-60 min',
    };
    return timeMap[businessType] || '30-60 min';
  };

  // Gestion des états d'erreur
  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor={colors.background.primary} barStyle="dark-content" />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleGoBack}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="Retour"
          >
            <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Types de Services</Text>
          
          <View style={styles.headerSpacer} />
        </View>

        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Erreur de chargement</Text>
          <Text style={styles.errorText}>
            Impossible de charger les types de services. Vérifiez votre connexion internet.
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => refetch()}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="Réessayer"
          >
            <Text style={styles.retryButtonText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={colors.background.primary} barStyle="dark-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleGoBack}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="Retour"
        >
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Sélectionner un service</Text>
        
        <View style={styles.headerSpacer} />
      </View>

      {/* Contenu principal */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
      >
        {/* Titre et sous-titre */}
        <View style={styles.titleContainer}>
          <Text style={styles.mainTitle}>Comment pouvons-nous vous aider ?</Text>
          <Text style={styles.subtitle}>Choisissez votre type de livraison</Text>
        </View>

        {/* Liste des types de services */}
        <View style={styles.servicesContainer}>
          {isLoading ? (
            // État de chargement
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Chargement des services...</Text>
            </View>
          ) : serviceTypes.length > 0 ? (
            // Liste des services
            serviceTypes.map(renderServiceTypeCard)
          ) : (
            // État vide
            <View style={styles.emptyContainer}>
              <Ionicons name="storefront" size={64} color={colors.text.disabled} />
              <Text style={styles.emptyTitle}>Aucun service disponible</Text>
              <Text style={styles.emptyText}>
                Les services de livraison ne sont pas encore disponibles dans votre zone.
              </Text>
            </View>
          )}
        </View>

        {/* Services Rentables - Nouveaux Services */}
        <View style={styles.enhancedServicesSection}>
          <View style={styles.enhancedServicesHeader}>
            <Text style={styles.enhancedServicesTitle}>🚀 Services Rentables</Text>
            <Text style={styles.enhancedServicesSubtitle}>
              Solutions adaptées au marché africain
            </Text>
          </View>

          <TouchableOpacity
            style={styles.enhancedServicesCard}
            onPress={() => navigation.navigate('EnhancedServicesScreen' as never)}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="Découvrir les services rentables"
          >
            <View style={styles.enhancedServicesContent}>
              <View style={styles.enhancedServicesIcon}>
                <Ionicons name="trending-up" size={32} color={colors.primary[500]} />
              </View>
              <View style={styles.enhancedServicesInfo}>
                <Text style={styles.enhancedServicesName}>Services B2B & Financiers</Text>
                <Text style={styles.enhancedServicesDescription}>
                  Logistique e-commerce, micro-crédits, assurance cash, Q-commerce
                </Text>
                <View style={styles.enhancedServicesBadges}>
                  <View style={styles.profitBadge}>
                    <Text style={styles.profitBadgeText}>Marge 35%+</Text>
                  </View>
                  <View style={styles.newBadge}>
                    <Text style={styles.newBadgeText}>NOUVEAU</Text>
                  </View>
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color={colors.text.secondary} />
            </View>
          </TouchableOpacity>

          {/* Aperçu des catégories */}
          <View style={styles.categoriesPreview}>
            {ENHANCED_SERVICE_CATEGORIES.slice(0, 2).map((category, index) => (
              <TouchableOpacity
                key={category.id}
                style={styles.categoryPreviewCard}
                onPress={() => navigation.navigate('EnhancedServicesScreen' as never)}
              >
                <View style={[styles.categoryPreviewIcon, { backgroundColor: category.color + '20' }]}>
                  <Ionicons name={category.icon as any} size={20} color={category.color} />
                </View>
                <Text style={styles.categoryPreviewName}>{category.name}</Text>
                <Text style={styles.categoryPreviewMargin}>{category.estimatedMargin}% marge</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Services Spécialisés - Nouvelle Section */}
        <View style={styles.specializedServicesSection}>
          <View style={styles.specializedServicesHeader}>
            <Text style={styles.specializedServicesTitle}>🎯 Services Spécialisés</Text>
            <Text style={styles.specializedServicesSubtitle}>
              Livraison professionnelle par secteur d'activité
            </Text>
          </View>

          <TouchableOpacity
            style={styles.specializedServicesCard}
            onPress={() => navigation.navigate('SpecializedServicesScreen' as never)}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="Découvrir les services spécialisés"
          >
            <View style={styles.specializedServicesContent}>
              <View style={styles.specializedServicesIcon}>
                <Ionicons name="briefcase" size={32} color={colors.secondary[500]} />
              </View>
              <View style={styles.specializedServicesInfo}>
                <Text style={styles.specializedServicesName}>8 Secteurs Spécialisés</Text>
                <Text style={styles.specializedServicesDescription}>
                  Colis, pharmacie, boulangerie, pressing, électronique, librairie, beauté, restaurant
                </Text>
                <View style={styles.specializedServicesBadges}>
                  <View style={styles.profitBadge}>
                    <Text style={styles.profitBadgeText}>Jusqu'à 40%</Text>
                  </View>
                  <View style={styles.professionalBadge}>
                    <Text style={styles.professionalBadgeText}>PROFESSIONNEL</Text>
                  </View>
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color={colors.text.secondary} />
            </View>
          </TouchableOpacity>

          {/* Aperçu des services spécialisés */}
          <View style={styles.specializedCategoriesPreview}>
            {SPECIALIZED_SERVICE_CATEGORIES.slice(0, 4).map((category, index) => (
              <TouchableOpacity
                key={category.id}
                style={styles.specializedCategoryCard}
                onPress={() => navigation.navigate('SpecializedServicesScreen' as never)}
              >
                <View style={[styles.specializedCategoryIcon, { backgroundColor: category.color + '20' }]}>
                  <Ionicons name={category.icon as any} size={18} color={category.color} />
                </View>
                <Text style={styles.specializedCategoryName}>{category.name}</Text>
                <Text style={styles.specializedCategoryMargin}>{category.estimatedMargin}%</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Information de zone de livraison */}
        <View style={styles.deliveryZoneContainer}>
          <View style={styles.deliveryZoneIcon}>
            <Ionicons name="location" size={20} color={colors.primary[500]} />
          </View>
          <View style={styles.deliveryZoneInfo}>
            <Text style={styles.deliveryZoneTitle}>Livraison disponible dans votre zone</Text>
            <Text style={styles.deliveryZoneAddress}>123 Avenue de l'Indépendance, Dakar</Text>
          </View>
        </View>

        {/* Espacement en bas */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginTop: 50, // FIX: Add top margin for status bar visibility
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 12,
    backgroundColor: colors.surface.secondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '700',
    color: colors.text.primary,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerSpacer: {
    width: 44,
  },

  // Contenu principal
  scrollView: {
    flex: 1,
  },
  titleContainer: {
    paddingHorizontal: 20,
    paddingTop: 24,
    paddingBottom: 32,
  },
  mainTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 8,
    lineHeight: 32,
  },
  subtitle: {
    fontSize: 16,
    color: colors.text.secondary,
    lineHeight: 24,
  },

  // Services
  servicesContainer: {
    paddingHorizontal: 20,
  },
  serviceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface.primary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  serviceIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  popularBadge: {
    alignSelf: 'flex-start',
    backgroundColor: colors.primary[500],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  popularBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.text.inverse,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  timeText: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  arrowContainer: {
    marginLeft: 8,
  },

  // Zone de livraison
  deliveryZoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface.secondary,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginTop: 32,
  },
  deliveryZoneIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  deliveryZoneInfo: {
    flex: 1,
  },
  deliveryZoneTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  deliveryZoneAddress: {
    fontSize: 14,
    color: colors.text.secondary,
    lineHeight: 20,
  },

  // États
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: colors.text.secondary,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: colors.primary[500],
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.inverse,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  bottomSpacing: {
    height: 40,
  },

  // Styles pour les services rentables
  enhancedServicesSection: {
    marginTop: 32,
    paddingHorizontal: 16,
  },
  enhancedServicesHeader: {
    marginBottom: 16,
  },
  enhancedServicesTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 4,
  },
  enhancedServicesSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  enhancedServicesCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.primary[200],
    elevation: 2,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  enhancedServicesContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  enhancedServicesIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  enhancedServicesInfo: {
    flex: 1,
  },
  enhancedServicesName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  enhancedServicesDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  enhancedServicesBadges: {
    flexDirection: 'row',
    gap: 8,
  },
  profitBadge: {
    backgroundColor: colors.success + '20',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  profitBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.success,
  },
  newBadge: {
    backgroundColor: colors.primary[500],
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  newBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.text.inverse,
  },
  categoriesPreview: {
    flexDirection: 'row',
    gap: 12,
  },
  categoryPreviewCard: {
    flex: 1,
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  categoryPreviewIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryPreviewName: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 4,
  },
  categoryPreviewMargin: {
    fontSize: 11,
    color: colors.success,
    fontWeight: '500',
  },

  // Styles pour les services spécialisés
  specializedServicesSection: {
    marginTop: 32,
    paddingHorizontal: 16,
  },
  specializedServicesHeader: {
    marginBottom: 16,
  },
  specializedServicesTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 4,
  },
  specializedServicesSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  specializedServicesCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.secondary[200],
    elevation: 2,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  specializedServicesContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  specializedServicesIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.secondary[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  specializedServicesInfo: {
    flex: 1,
  },
  specializedServicesName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  specializedServicesDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  specializedServicesBadges: {
    flexDirection: 'row',
    gap: 8,
  },
  professionalBadge: {
    backgroundColor: colors.secondary[500],
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  professionalBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.text.inverse,
  },
  specializedCategoriesPreview: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  specializedCategoryCard: {
    flex: 1,
    minWidth: '22%',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.neutral[200],
  },
  specializedCategoryIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  specializedCategoryName: {
    fontSize: 10,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 2,
  },
  specializedCategoryMargin: {
    fontSize: 9,
    color: colors.success,
    fontWeight: '500',
  },
});

export default ServiceTypeScreen;
