import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { ClientStackParamList } from '../types';
import { colors, dimensions, typography, spacing } from '../constants/theme';
import { useCart } from '../hooks/useCartWrapper';

// Écrans clients
import HomeScreen from '../screens/client/HomeScreen';
import SearchScreen from '../screens/client/SearchScreen';
import OrdersScreen from '../screens/client/OrdersScreen';
import ProfileScreen from '../screens/client/ProfileScreen';
import LocationScreenClean from '../screens/client/LocationScreenClean';
import CartScreen from '../screens/client/CartScreen';
import { RestaurantDetailScreen } from '../screens/client/RestaurantDetailScreen';
import ServiceTypeScreen from '../screens/client/ServiceTypeScreen';
import OrderTrackingScreen from '../screens/client/OrderTrackingScreen';
import AddressesScreen from '../screens/client/AddressesScreen';
import AddAddressScreen from '../screens/client/AddAddressScreen';
import EditProfileScreen from '../screens/shared/EditProfileScreen';
import PreferencesScreen from '../screens/shared/PreferencesScreen';
import EnhancedServicesScreen from '../screens/shared/EnhancedServicesScreen';
import ServiceRequestScreen from '../screens/shared/ServiceRequestScreen';
import ProfitabilityDashboard from '../screens/shared/ProfitabilityDashboard';
import SpecializedServicesScreen from '../screens/shared/SpecializedServicesScreen';
import ServiceOrderScreen from '../screens/shared/ServiceOrderScreen';
import MapScreen from '../screens/client/MapScreen';
import NotificationsScreen from '../screens/client/NotificationsScreen';
import PaymentScreen from '../screens/client/PaymentScreen';
import CategoryRestaurantsScreen from '../screens/client/CategoryRestaurantsScreen';
import ProductDetailScreen from '../screens/client/ProductDetailScreen';
import RestaurantListScreen from '../screens/client/RestaurantListScreen';
import CheckoutScreen from '../screens/client/CheckoutScreen';
import HelpScreen from '../screens/client/HelpScreen';


const Tab = createBottomTabNavigator();
const Stack = createStackNavigator<ClientStackParamList>();

// Composant Badge pour les notifications
const TabBarBadge: React.FC<{ count: number }> = ({ count }) => {
  if (count === 0) return null;
  
  return (
    <View style={styles.badge}>
      <Text style={styles.badgeText}>
        {count > 99 ? '99+' : count.toString()}
      </Text>
    </View>
  );
};

// Navigation par onglets
const TabNavigator: React.FC = () => {
  const { totalItems } = useCart();
  const cartItemsCount = totalItems;

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Search':
              iconName = focused ? 'search' : 'search-outline';
              break;
            case 'Orders':
              iconName = focused ? 'receipt' : 'receipt-outline';
              break;
            case 'Profile':
              iconName = focused ? 'person' : 'person-outline';
              break;
            default:
              iconName = 'home-outline';
          }

          return (
            <View style={styles.tabIconContainer}>
              <Ionicons name={iconName} size={size} color={color} />
              {route.name === 'Home' && cartItemsCount > 0 && (
                <TabBarBadge count={cartItemsCount} />
              )}
            </View>
          );
        },
        tabBarActiveTintColor: colors.primary[500],
        tabBarInactiveTintColor: colors.neutral[500],
        tabBarStyle: styles.tabBar,
        tabBarLabelStyle: styles.tabBarLabel,
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: 'Accueil',
          tabBarLabel: 'Accueil'
        }}
      />
      <Tab.Screen 
        name="Search" 
        component={SearchScreen}
        options={{ 
          title: 'Rechercher',
          tabBarLabel: 'Rechercher'
        }}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrdersScreen}
        options={{ 
          title: 'Commandes',
          tabBarLabel: 'Commandes'
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'Profil',
          tabBarLabel: 'Profil'
        }}
      />

    </Tab.Navigator>
  );
};

// Navigation principale avec pile pour les détails
const ClientNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary[500],
          elevation: 0,
          shadowOpacity: 0,
        },
        headerTintColor: colors.text.inverse,
        headerTitleStyle: {
          fontWeight: typography.fontWeight.bold,
          fontSize: typography.fontSize.lg,
        },
        headerBackTitleVisible: false,
        cardStyle: { backgroundColor: colors.background.primary },
      }}
    >
      {/* Navigation par onglets comme écran principal */}
      <Stack.Screen 
        name="HomeTabs" 
        component={TabNavigator}
        options={{ headerShown: false }}
      />
      
      {/* Écrans de détail avec navigation en pile */}
      <Stack.Screen
        name="Establishment"
        component={RestaurantDetailScreen}
        options={{
          title: 'Restaurant',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="CategoryRestaurants"
        component={CategoryRestaurantsScreen}
        options={{
          title: 'Restaurants',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="ServiceTypes"
        component={ServiceTypeScreen}
        options={{
          title: 'Types de Services',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="RestaurantList"
        component={RestaurantListScreen}
        options={{
          title: 'Restaurants & Boutiques',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="Product"
        component={ProductDetailScreen}
        options={{
          title: 'Détails du produit',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="Cart"
        component={CartScreen}
        options={{
          title: 'Panier',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="CartScreen"
        component={CartScreen}
        options={{
          title: 'Panier',
          headerShown: false,
        }}
      />
      
      <Stack.Screen
        name="Checkout"
        component={CheckoutScreen}
        options={{
          title: 'Finaliser la commande',
          headerShown: false,
        }}
      />
      
      <Stack.Screen
        name="OrderTracking"
        component={OrderTrackingScreen}
        options={{
          title: 'Suivi de commande',
          headerShown: false,
        }}
      />
      
      <Stack.Screen
        name="Location"
        component={LocationScreenClean}
        options={{
          title: 'Localisation',
          headerShown: false,
        }}
      />



      <Stack.Screen
        name="Addresses"
        component={AddressesScreen}
        options={{
          title: 'Mes adresses',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="AddAddress"
        component={AddAddressScreen}
        options={{
          title: 'Ajouter une adresse',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{
          title: 'Modifier le profil',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="Preferences"
        component={PreferencesScreen}
        options={{
          title: 'Préférences',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="Map"
        component={MapScreen}
        options={{
          title: 'Carte des restaurants',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          title: 'Notifications',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="Payment"
        component={PaymentScreen}
        options={{
          title: 'Paiement',
          headerShown: false,
        }}
      />
      
      <Stack.Screen
        name="Help"
        component={HelpScreen}
        options={{
          title: 'Aide et Support',
          headerShown: false,
        }}
      />

      {/* Nouveaux services rentables */}
      <Stack.Screen
        name="EnhancedServicesScreen"
        component={EnhancedServicesScreen}
        options={{
          title: 'Services Rentables',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="ServiceRequestForm"
        component={ServiceRequestScreen}
        options={{
          title: 'Demande de Service',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="ProfitabilityDashboard"
        component={ProfitabilityDashboard}
        options={{
          title: 'Tableau de Rentabilité',
          headerShown: false,
        }}
      />

      {/* Services spécialisés */}
      <Stack.Screen
        name="SpecializedServicesScreen"
        component={SpecializedServicesScreen}
        options={{
          title: 'Services Spécialisés',
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="ServiceOrderForm"
        component={ServiceOrderScreen}
        options={{
          title: 'Commander un Service',
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: colors.surface.primary,
    borderTopColor: colors.border.light,
    borderTopWidth: 1,
    height: dimensions.tabBarHeight,
    paddingBottom: spacing.sm,
    paddingTop: spacing.sm,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },

  tabBarLabel: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    marginTop: 2,
  },

  tabIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },

  badge: {
    position: 'absolute',
    top: -6,
    right: -12,
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },

  badgeText: {
    color: colors.text.inverse,
    fontSize: 10,
    fontWeight: typography.fontWeight.bold,
    textAlign: 'center',
  },
});

export default ClientNavigator; 